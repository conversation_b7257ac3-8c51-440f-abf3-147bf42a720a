﻿import os
import codecs

def convert_file_encoding(file_path, source_encoding='utf-8', target_encoding='gb2312'):
    try:
        # 读取源文件内容
        with codecs.open(file_path, 'r', encoding=source_encoding) as source_file:
            content = source_file.read()
        
        # 写入目标文件内容
        with codecs.open(file_path, 'w', encoding=target_encoding) as target_file:
            target_file.write(content)
        
        print(f"Converted: {file_path}")
    except Exception as e:
        print(f"Error converting {file_path}: {e}")

def main(directory):
    # 遍历指定目录下的所有文件
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.h') or file.endswith('.cpp'):
                file_path = os.path.join(root, file)
                convert_file_encoding(file_path)

if __name__ == '__main__':
    import sys
    
    directory = os.path.dirname(os.path.abspath(__file__))
    main(directory)