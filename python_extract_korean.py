import os
import re
import json
import codecs
import chardet
from pathlib import Path

# 韩文字符范围的正则表达式 - 包括Unicode韩文和非UTF-8编码的韩文（通常显示为乱码）
# 使用更精确的模式匹配韩文字符
# \uAC00-\uD7AF: 韩文拼音（Hangul Syllables）
# \u1100-\u11FF: 韩文字母（Hangul Jamo）
# \u3130-\u318F: 韩文兼容字母（Hangul Compatibility Jamo）
# 对于非UTF-8编码的韩文，我们需要更精确的模式，避免误判
KOREAN_PATTERN = re.compile(r'[가-힣]')


# 要扫描的文件扩展名
FILE_EXTENSIONS = ('.cpp', '.h', '.c', '.hpp')

# 要排除的目录
EXCLUDE_DIRS = ('_Bin', 'Mir2Ex___Win32_Test_Debug', 'Mir2Ex___Win32_Test_Release')

# 结果保存路径
RESULT_JSON_PATH = 'korean_text_info.json'
RESULT_TXT_PATH = 'korean_text_for_translation.txt'

def detect_encoding(file_path):
    """检测文件编码"""
    with open(file_path, 'rb') as f:
        result = chardet.detect(f.read())
    return result['encoding']

def is_likely_korean(line, encoding):
    """检查文本是否包含韩文字符"""
    return bool(KOREAN_PATTERN.search(line))

def scan_files(root_dir):
    """扫描目录下的所有文件，提取包含韩文的行"""
    results = []  # 用于JSON文件的完整信息
    translation_lines = []  # 用于翻译文本文件的简化内容
    line_counter = 0
    
    # 额外的排除文件列表
    exclude_files = ['translation_guide.md', 'extract_korean.py', 'update_translations.py', 'magic.txt', 'magic-chinese.txt']
    
    root_path = Path(root_dir)
    
    for file_path in root_path.glob('**/*'):
        # 跳过目录和非目标文件
        if file_path.is_dir():
            continue
        if file_path.suffix.lower() not in FILE_EXTENSIONS:
            continue
        if any(exclude_dir in str(file_path) for exclude_dir in EXCLUDE_DIRS):
            continue
        if file_path.name in exclude_files:
            print(f"跳过文件: {file_path}")
            continue
        
        try:
            # 尝试以不同编码读取文件
            file_content = None
            used_encoding = None
            
            encoding = detect_encoding(file_path)
            
            if not encoding:
                encoding = 'cp949'  # 韩文Windows默认编码
                
            with codecs.open(file_path, 'r', encoding=encoding, errors='replace') as f:
                file_content = f.read()
                
            used_encoding = encoding
            
            if file_content is None:
                print(f"无法解码文件: {file_path}")
                continue
            
            # 按行处理文件内容
            lines = file_content.split('\n')
            
            for line_num, line in enumerate(lines, 1):
                # 跳过空行和只包含空白字符的行
                if not line.strip():
                    continue
                
                if is_likely_korean(line, used_encoding):
                    # 创建结果记录 - 只包含文件名、行号和位置信息
                    result = {
                        'file_path': str(file_path),
                        'line_number': line_num,
                        'encoding': used_encoding,
                        'id': line_counter
                    }
                    results.append(result)
                    
                    # 只添加韩文内容到翻译文本，使用ID作为标识
                    # 去除行首行尾的空白字符
                    cleaned_line = line.strip()
                    translation_lines.append(f"{line_counter}={cleaned_line}\n")
                    line_counter += 1
        
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {e}")
    
    return results, translation_lines

def main():
    root_dir = os.path.dirname(os.path.abspath(__file__))
    print(f"开始扫描目录: {root_dir}")
    
    results, translation_lines = scan_files(root_dir)
    
    # 保存JSON结果 - 只包含文件名、行号和位置信息
    with open(os.path.join(root_dir, RESULT_JSON_PATH), 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    # 保存翻译文本 - 只包含ID和韩文内容
    with open(os.path.join(root_dir, RESULT_TXT_PATH), 'w', encoding='utf-8') as f:
        f.writelines(translation_lines)
    
    print(f"扫描完成，共找到 {len(results)} 行包含韩文的文本")
    print(f"结果已保存到 {RESULT_JSON_PATH} 和 {RESULT_TXT_PATH}")
    print(f"在 {RESULT_JSON_PATH} 中，只包含文件名、行号和位置信息")
    print(f"在 {RESULT_TXT_PATH} 中，每行格式为: ID=韩文内容")
    print(f"请将翻译后的内容保存为 translated_text.txt，保持相同的格式: ID=翻译内容")

if __name__ == '__main__':
    main()