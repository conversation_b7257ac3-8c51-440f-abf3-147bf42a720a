import os
import json
import codecs
import re
from pathlib import Path

# 结果文件路径
RESULT_JSON_PATH = 'korean_text_info.json'
# 翻译后的文本文件路径 - 用户需要在运行此脚本前提供此文件
TRANSLATED_TXT_PATH = 'translated_text.txt'
# 备份目录
BACKUP_DIR = 'original_files_backup'

def load_json_info():
    """加载包含韩文文本信息的JSON文件"""
    with open(RESULT_JSON_PATH, 'r', encoding='utf-8') as f:
        return json.load(f)

def parse_translated_file(file_path):
    """解析翻译后的文本文件，返回ID到翻译文本的映射"""
    translations = {}
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 解析格式: ID=翻译内容
            parts = line.split('=', 1)
            if len(parts) == 2:
                try:
                    item_id = int(parts[0])
                    translation = parts[1]
                    translations[item_id] = translation
                except ValueError:
                    print(f"警告: 无法解析行: {line}")
    
    except Exception as e:
        print(f"解析翻译文件时出错: {e}")
    
    return translations

def create_backup(file_path):
    """创建文件备份"""
    backup_path = Path(BACKUP_DIR) / Path(file_path).name
    os.makedirs(os.path.dirname(backup_path), exist_ok=True)
    
    try:
        with open(file_path, 'rb') as src_file:
            with open(backup_path, 'wb') as dst_file:
                dst_file.write(src_file.read())
        return True
    except Exception as e:
        print(f"创建备份失败 {file_path}: {e}")
        return False

def update_files(json_info, translations):
    """更新源文件中的文本"""
    # 按文件路径分组
    files_to_update = {}
    for item in json_info:
        file_path = item['file_path']
        if file_path not in files_to_update:
            files_to_update[file_path] = []
        files_to_update[file_path].append(item)
    
    # 确保按行号降序排序，这样从后向前替换不会影响行号
    for file_path, items in files_to_update.items():
        items.sort(key=lambda x: x['line_number'], reverse=True)
    
    # 创建备份目录
    os.makedirs(BACKUP_DIR, exist_ok=True)
    
    # 更新每个文件
    updated_files = 0
    for file_path, items in files_to_update.items():
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                print(f"文件不存在: {file_path}")
                continue
            
            # 创建备份
            if not create_backup(file_path):
                continue
            
            # 读取文件内容
            encoding = items[0]['encoding']  # 使用之前检测到的编码
            with codecs.open(file_path, 'r', encoding=encoding) as f:
                lines = f.readlines()
            
            # 从后向前更新行，这样不会影响行号
            for item in items:
                line_num = item['line_number'] - 1  # 转为0索引
                item_id = item['id']
                
                if item_id in translations:
                    # 保持原始的缩进和行尾
                    original_line = lines[line_num]
                    indent_match = re.match(r'^(\s*)', original_line)
                    indent = indent_match.group(1) if indent_match else ''
                    
                    # 检测行尾
                    line_end = '\r\n' if original_line.endswith('\r\n') else '\n'
                    
                    # 替换行内容，保持缩进和行尾
                    lines[line_num] = f"{indent}{translations[item_id]}{line_end}"
            
            # 写回文件，使用UTF-8编码
            with codecs.open(file_path, 'w', encoding='utf-8') as f:
                f.writelines(lines)
            
            updated_files += 1
            print(f"已更新文件: {file_path}")
            
        except Exception as e:
            print(f"更新文件 {file_path} 时出错: {e}")
    
    return updated_files

def create_translation_template():
    """创建翻译模板文件"""
    if not os.path.exists(RESULT_JSON_PATH):
        print(f"错误: 找不到JSON信息文件 {RESULT_JSON_PATH}")
        return
    
    json_info = load_json_info()
    template_path = 'translation_template.txt'
    
    # 从korean_text_for_translation.txt读取原始韩文内容
    korean_text_path = 'korean_text_for_translation.txt'
    korean_content = {}
    
    try:
        with open(korean_text_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if not line:
                    continue
                
                parts = line.split('=', 1)
                if len(parts) == 2:
                    try:
                        item_id = int(parts[0])
                        content = parts[1]
                        korean_content[item_id] = content
                    except ValueError:
                        print(f"警告: 无法解析韩文内容行: {line}")
    except Exception as e:
        print(f"读取韩文内容文件时出错: {e}")
        return
    
    # 创建翻译模板
    with open(template_path, 'w', encoding='utf-8') as f:
        for item in json_info:
            item_id = item['id']
            if item_id in korean_content:
                # 使用简化格式: ID=内容
                f.write(f"{item_id}={korean_content[item_id]}\n")
    
    print(f"已创建翻译模板文件: {template_path}")
    print(f"请编辑此文件，将韩文内容替换为翻译后的文本，保持格式: ID=翻译内容")
    print(f"然后将文件重命名为 {TRANSLATED_TXT_PATH}")

def main():
    root_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(root_dir)
    
    # 检查JSON文件是否存在
    if not os.path.exists(RESULT_JSON_PATH):
        print(f"错误: 找不到JSON信息文件 {RESULT_JSON_PATH}")
        print("请先运行 extract_korean.py 脚本生成信息文件")
        return
    
    # 检查翻译文件是否存在
    if not os.path.exists(TRANSLATED_TXT_PATH):
        print(f"错误: 找不到翻译文件 {TRANSLATED_TXT_PATH}")
        print("正在创建翻译模板文件...")
        create_translation_template()
        return
    
    # 加载信息和翻译
    json_info = load_json_info()
    translations = parse_translated_file(TRANSLATED_TXT_PATH)
    
    if not translations:
        print("错误: 翻译文件为空或格式不正确")
        return
    
    print(f"已加载 {len(json_info)} 条原始文本信息和 {len(translations)} 条翻译")
    
    # 更新文件
    updated_files = update_files(json_info, translations)
    
    print(f"更新完成，共更新了 {updated_files} 个文件")
    print(f"原始文件已备份到 {BACKUP_DIR} 目录")

if __name__ == '__main__':
    main()