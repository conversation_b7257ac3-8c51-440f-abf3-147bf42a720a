<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <SccProjectName>"$/Mir2Ex", BAAAAAAA</SccProjectName>
    <SccLocalPath>.</SccLocalPath>
    <ProjectGuid>{D01C8C60-307C-4905-8552-E74D5222CD8E}</ProjectGuid>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <PlatformToolset>v143</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <PlatformToolset>v143</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.Cpp.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.Cpp.UpgradeFromVC60.props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <OutDir>.\_Bin\</OutDir>
    <IntDir>.\Mir2Ex___Win32_Test_Debug\</IntDir>
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>$(ProjectDir);$(VC_IncludePath);$(WindowsSDK_IncludePath);$(DXSDK_DIR)Include</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(DXSDK_DIR)Lib\x86;</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <OutDir>.\_Bin\</OutDir>
    <IntDir>.\Mir2Ex___Win32_Test_Release\</IntDir>
    <LinkIncremental>false</LinkIncremental>
    <IncludePath>$(ProjectDir);$(VC_IncludePath);$(WindowsSDK_IncludePath);$(DXSDK_DIR)Include</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(DXSDK_DIR)Lib\x86;</LibraryPath>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <Optimization>Disabled</Optimization>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <MinimalRebuild>true</MinimalRebuild>
      <DebugInformationFormat>EditAndContinue</DebugInformationFormat>
      <AdditionalIncludeDirectories>./_Oranze Library;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AssemblerListingLocation>.\Mir2Ex___Win32_Test_Debug\</AssemblerListingLocation>
      <BrowseInformation>true</BrowseInformation>
      <PrecompiledHeaderOutputFile>.\Mir2Ex___Win32_Test_Debug\Mir2Ex.pch</PrecompiledHeaderOutputFile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>StdAfx.h</PrecompiledHeaderFile>
      <ObjectFileName>.\Mir2Ex___Win32_Test_Debug\</ObjectFileName>
      <ProgramDataBaseFileName>.\Mir2Ex___Win32_Test_Debug\</ProgramDataBaseFileName>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <Midl>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <TypeLibraryName>.\Mir2Ex___Win32_Test_Debug\Mir2Ex.tlb</TypeLibraryName>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <TargetEnvironment>Win32</TargetEnvironment>
    </Midl>
    <ResourceCompile>
      <Culture>0x0412</Culture>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Mir2Ex___Win32_Test_Debug\Mir2Ex.bsc</OutputFile>
    </Bscmake>
    <Link>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <AdditionalDependencies>odbc32.lib;odbccp32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <StringPooling>true</StringPooling>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <Optimization>MaxSpeed</Optimization>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWarningAsError>false</TreatWarningAsError>
      <AdditionalIncludeDirectories>./_Oranze Library;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AssemblerListingLocation>.\Mir2Ex___Win32_Test_Release\</AssemblerListingLocation>
      <BrowseInformation>true</BrowseInformation>
      <PrecompiledHeaderOutputFile>.\Mir2Ex___Win32_Test_Release\Mir2Ex.pch</PrecompiledHeaderOutputFile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>StdAfx.h</PrecompiledHeaderFile>
      <ObjectFileName>.\Mir2Ex___Win32_Test_Release\</ObjectFileName>
      <ProgramDataBaseFileName>.\Mir2Ex___Win32_Test_Release\</ProgramDataBaseFileName>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <Midl>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <TypeLibraryName>.\Mir2Ex___Win32_Test_Release\Mir2Ex.tlb</TypeLibraryName>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <TargetEnvironment>Win32</TargetEnvironment>
    </Midl>
    <ResourceCompile>
      <Culture>0x0412</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Mir2Ex___Win32_Test_Release\Mir2Ex.bsc</OutputFile>
    </Bscmake>
    <Link>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <SubSystem>Windows</SubSystem>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <AdditionalDependencies>odbc32.lib;odbccp32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="AVI\Avi.cpp" />
    <ClCompile Include="CharSelectProcess\ChrProc.cpp" />
    <ClCompile Include="CmdLineParser.cpp" />
    <ClCompile Include="Common\ChatEditBox.cpp" />
    <ClCompile Include="Common\ClientSocket.cpp" />
    <ClCompile Include="Common\crypto.cpp" />
    <ClCompile Include="Common\DblList.cpp" />
    <ClCompile Include="Common\DLinkedList.cpp" />
    <ClCompile Include="Common\EnDecode.cpp" />
    <ClCompile Include="Common\ImageHandler.cpp" />
    <ClCompile Include="Common\ImageHandlerHelper.cpp" />
    <ClCompile Include="Common\Msg.cpp" />
    <ClCompile Include="Common\NoticeBox.cpp" />
    <ClCompile Include="Common\Queue.cpp" />
    <ClCompile Include="Common\ScreenCapture.cpp" />
    <ClCompile Include="Common\StringSplitter.cpp" />
    <ClCompile Include="GameProcess\Actor.cpp" />
    <ClCompile Include="GameProcess\BeltWnd.cpp" />
    <ClCompile Include="GameProcess\ChatPopWnd.cpp" />
    <ClCompile Include="GameProcess\ChatWnd.cpp" />
    <ClCompile Include="GameProcess\ClientSysMsg.cpp" />
    <ClCompile Include="GameProcess\ExchangeWnd.cpp" />
    <ClCompile Include="GameProcess\FaceImgUp.cpp" />
    <ClCompile Include="GameProcess\GameBtn.cpp" />
    <ClCompile Include="GameProcess\GameMsgBox.cpp" />
    <ClCompile Include="GameProcess\GameOverWnd.cpp" />
    <ClCompile Include="GameProcess\GameProc.cpp" />
    <ClCompile Include="GameProcess\GameWnd.cpp" />
    <ClCompile Include="GameProcess\GroupWnd.cpp" />
    <ClCompile Include="GameProcess\GuildWnd.cpp" />
    <ClCompile Include="GameProcess\HorseWnd.cpp" />
    <ClCompile Include="GameProcess\Interface.cpp" />
    <ClCompile Include="GameProcess\InventoryExWnd.cpp" />
    <ClCompile Include="GameProcess\InventoryWnd.cpp" />
    <ClCompile Include="GameProcess\Item.cpp" />
    <ClCompile Include="GameProcess\LightFog.cpp" />
    <ClCompile Include="GameProcess\Magic.cpp" />
    <ClCompile Include="GameProcess\MagicShortcutWnd.cpp" />
    <ClCompile Include="GameProcess\MagicWnd.cpp" />
    <ClCompile Include="GameProcess\MapHandler.cpp" />
    <ClCompile Include="GameProcess\Market.cpp" />
    <ClCompile Include="GameProcess\MarketEx.cpp" />
    <ClCompile Include="GameProcess\MarketUp.cpp" />
    <ClCompile Include="GameProcess\Messenger.cpp" />
    <ClCompile Include="GameProcess\MiniMap.cpp" />
    <ClCompile Include="GameProcess\MiniMapInSiege.cpp" />
    <ClCompile Include="GameProcess\NoticeEditWnd.cpp" />
    <ClCompile Include="GameProcess\NPCWnd.cpp" />
    <ClCompile Include="GameProcess\OptionWnd.cpp" />
    <ClCompile Include="GameProcess\Particle.cpp" />
    <ClCompile Include="GameProcess\PathFinding.cpp" />
    <ClCompile Include="GameProcess\QuestWnd.cpp" />
    <ClCompile Include="GameProcess\SettingWnd.cpp" />
    <ClCompile Include="GameProcess\SiegeWnd.cpp" />
    <ClCompile Include="GameProcess\SprDfn.cpp" />
    <ClCompile Include="GameProcess\StatusWnd.cpp" />
    <ClCompile Include="GameProcess\StoreWnd.cpp" />
    <ClCompile Include="GameProcess\UserStateWnd.cpp" />
    <ClCompile Include="GameProcess\UtilWnd.cpp" />
    <ClCompile Include="GameProcess\ViewMiniMapWnd.cpp" />
    <ClCompile Include="GFun.cpp" />
    <ClCompile Include="LoginProcess\LoginProc.cpp" />
    <ClCompile Include="Mir2Ex.cpp" />
    <ClCompile Include="Sound\BMMP3.cpp" />
    <ClCompile Include="Sound\DSound.cpp" />
    <ClCompile Include="Sound\SoundManager.cpp" />
    <ClCompile Include="Sound\WaveBuffer.cpp" />
    <ClCompile Include="StdAfx.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeaderFile Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">stdafx.h</PrecompiledHeaderFile>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeaderFile Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">stdafx.h</PrecompiledHeaderFile>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="Resource.rc" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="AVI\Avi.h" />
    <ClInclude Include="CharSelectProcess\ChrProc.h" />
    <ClInclude Include="CmdLineParser.h" />
    <ClInclude Include="Common\ChatEditBox.h" />
    <ClInclude Include="Common\ClientSocket.h" />
    <ClInclude Include="Common\crypto.h" />
    <ClInclude Include="Common\DblList.h" />
    <ClInclude Include="Common\DLinkedList.h" />
    <ClInclude Include="Common\EnDecode.h" />
    <ClInclude Include="Common\ImageHandler.h" />
    <ClInclude Include="Common\ImageHandlerHelper.h" />
    <ClInclude Include="Common\list.h" />
    <ClInclude Include="Common\Msg.h" />
    <ClInclude Include="Common\NoticeBox.h" />
    <ClInclude Include="Common\Protocol.h" />
    <ClInclude Include="Common\Queue.h" />
    <ClInclude Include="Common\ScreenCapture.h" />
    <ClInclude Include="Common\StringSplitter.h" />
    <ClInclude Include="Common\Typedeftxt.h" />
    <ClInclude Include="Define.h" />
    <ClInclude Include="Extern.h" />
    <ClInclude Include="GameProcess\Actor.h" />
    <ClInclude Include="GameProcess\BeltWnd.h" />
    <ClInclude Include="GameProcess\ChatPopWnd.h" />
    <ClInclude Include="GameProcess\ChatWnd.h" />
    <ClInclude Include="GameProcess\ClientSysMsg.h" />
    <ClInclude Include="GameProcess\ExchangeWnd.h" />
    <ClInclude Include="GameProcess\FaceImgUp.h" />
    <ClInclude Include="GameProcess\GameBtn.h" />
    <ClInclude Include="GameProcess\GameMsgBox.h" />
    <ClInclude Include="GameProcess\GameOverWnd.h" />
    <ClInclude Include="GameProcess\GameProc.h" />
    <ClInclude Include="GameProcess\GameWnd.h" />
    <ClInclude Include="GameProcess\GroupWnd.h" />
    <ClInclude Include="GameProcess\GuildWnd.h" />
    <ClInclude Include="GameProcess\HorseWnd.h" />
    <ClInclude Include="GameProcess\Interface.h" />
    <ClInclude Include="GameProcess\InventoryExWnd.h" />
    <ClInclude Include="GameProcess\InventoryWnd.h" />
    <ClInclude Include="GameProcess\Item.h" />
    <ClInclude Include="GameProcess\LightFog.h" />
    <ClInclude Include="GameProcess\Magic.h" />
    <ClInclude Include="GameProcess\MagicShortcutWnd.h" />
    <ClInclude Include="GameProcess\MagicWnd.h" />
    <ClInclude Include="GameProcess\MapHandler.h" />
    <ClInclude Include="GameProcess\Market.h" />
    <ClInclude Include="GameProcess\MarketEx.h" />
    <ClInclude Include="GameProcess\MarketUp.h" />
    <ClInclude Include="GameProcess\Messenger.h" />
    <ClInclude Include="GameProcess\MiniMap.h" />
    <ClInclude Include="GameProcess\MiniMapInSiege.h" />
    <ClInclude Include="GameProcess\NoticeEditWnd.h" />
    <ClInclude Include="GameProcess\NPCWnd.h" />
    <ClInclude Include="GameProcess\OptionWnd.h" />
    <ClInclude Include="GameProcess\Particle.h" />
    <ClInclude Include="GameProcess\PathFinding.h" />
    <ClInclude Include="GameProcess\QuestWnd.h" />
    <ClInclude Include="GameProcess\SettingWnd.h" />
    <ClInclude Include="GameProcess\SiegeWnd.h" />
    <ClInclude Include="GameProcess\SprDfn.h" />
    <ClInclude Include="GameProcess\StatusWnd.h" />
    <ClInclude Include="GameProcess\StoreWnd.h" />
    <ClInclude Include="GameProcess\UserStateWnd.h" />
    <ClInclude Include="GameProcess\UtilWnd.h" />
    <ClInclude Include="GameProcess\ViewMiniMapWnd.h" />
    <ClInclude Include="GFun.h" />
    <ClInclude Include="LoginProcess\LoginProc.h" />
    <ClInclude Include="Sound\BMMP3.h" />
    <ClInclude Include="Sound\DSound.h" />
    <ClInclude Include="Sound\SoundManager.h" />
    <ClInclude Include="Sound\TypeDef.h" />
    <ClInclude Include="Sound\WaveBuffer.h" />
    <ClInclude Include="StdAfx.h" />
  </ItemGroup>
  <ItemGroup>
    <Text Include="ReadMe.txt" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="WindHorn\WindHorn.vcxproj">
      <Project>{79836a68-3e67-41e3-a617-ad200c7744fe}</Project>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Library Include="_Oranze Library\_Bin\_Oranze Library.lib" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>