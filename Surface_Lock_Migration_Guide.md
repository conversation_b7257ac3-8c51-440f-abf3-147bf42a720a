# 表面锁定代码迁移指南

## 概述

本文档提供了将DirectX 7.0的DDSURFACEDESC2表面锁定代码迁移到DirectX 9.0的D3DLOCKED_RECT的详细指南。

## 迁移模式

### 1. 基本结构替换

#### DirectX 7.0 模式：
```cpp
DDSURFACEDESC2 ddsd;
ZeroMemory(&ddsd, sizeof(DDSURFACEDESC2));
ddsd.dwSize = sizeof(DDSURFACEDESC2);
if (FAILED(surface->Lock(NULL, &ddsd, DDLOCK_WAIT | DDLOCK_SURFACEMEMORYPTR, NULL)))
{
    return FALSE;
}

WORD* pwdDst = (WORD*)ddsd.lpSurface;
LONG lWidth = ddsd.lPitch >> 1;

// ... 图像处理代码 ...

surface->Unlock(NULL);
```

#### DirectX 9.0 模式：
```cpp
D3DLOCKED_RECT lockedRect;
if (FAILED(surface->LockRect(&lockedRect, NULL, 0)))
{
    return FALSE;
}

WORD* pwdDst = (WORD*)lockedRect.pBits;
LONG lWidth = lockedRect.Pitch >> 1;

// ... 图像处理代码 ...

surface->UnlockRect();
```

### 2. 关键替换对照表

| DirectX 7.0 | DirectX 9.0 | 说明 |
|-------------|-------------|------|
| `DDSURFACEDESC2 ddsd` | `D3DLOCKED_RECT lockedRect` | 锁定结构 |
| `ZeroMemory(&ddsd, sizeof(DDSURFACEDESC2))` | 不需要 | DirectX 9.0不需要初始化 |
| `ddsd.dwSize = sizeof(DDSURFACEDESC2)` | 不需要 | DirectX 9.0不需要设置大小 |
| `surface->Lock(NULL, &ddsd, flags, NULL)` | `surface->LockRect(&lockedRect, NULL, 0)` | 锁定方法 |
| `ddsd.lpSurface` | `lockedRect.pBits` | 表面数据指针 |
| `ddsd.lPitch` | `lockedRect.Pitch` | 行间距 |
| `surface->Unlock(NULL)` | `surface->UnlockRect()` | 解锁方法 |

### 3. 常见锁定标志迁移

DirectX 7.0的锁定标志在DirectX 9.0中大多不需要或有所简化：

- `DDLOCK_WAIT` → 不需要（DirectX 9.0自动等待）
- `DDLOCK_SURFACEMEMORYPTR` → 不需要
- `DDLOCK_WRITEONLY` → `D3DLOCK_DISCARD` 或 `0`
- `DDLOCK_READONLY` → `D3DLOCK_READONLY`

## 需要迁移的方法列表

### 已完成 ✅
1. `DrawWithGrayBackBuffer()` - 灰度后缓冲绘制
2. `DrawWithImageForComp()` - 压缩图像绘制

### 待迁移 📝
3. `DrawWithImageEx()` - 扩展图像绘制
4. `DrawWithImageExForComp()` - 压缩扩展图像绘制
5. `DrawWithImageExForCompBlend()` - 混合压缩扩展图像绘制
6. `DrawWithImageExForCompBlendShadow()` - 阴影混合压缩扩展图像绘制
7. `DrawWithImageExForCompBlendLight()` - 光照混合压缩扩展图像绘制
8. `DrawWithImageExForCompBlendAdd()` - 加法混合压缩扩展图像绘制
9. `DrawWithImageExForCompBlendMul()` - 乘法混合压缩扩展图像绘制
10. `DrawWithImageExForCompBlendSub()` - 减法混合压缩扩展图像绘制
11. `DrawWithImageExForCompBlendDiv()` - 除法混合压缩扩展图像绘制
12. `DrawWithImageExForCompBlendScreen()` - 屏幕混合压缩扩展图像绘制
13. `DrawWithImageExForCompBlendOverlay()` - 覆盖混合压缩扩展图像绘制
14. `DrawWithImageExForCompBlendHardLight()` - 强光混合压缩扩展图像绘制
15. `DrawWithImageExForCompBlendSoftLight()` - 柔光混合压缩扩展图像绘制
16. `DrawWithImageExForCompBlendColorDodge()` - 颜色减淡混合压缩扩展图像绘制
17. `DrawWithImageExForCompBlendColorBurn()` - 颜色加深混合压缩扩展图像绘制
18. `DrawWithImageExForCompBlendDarken()` - 变暗混合压缩扩展图像绘制
19. `DrawWithImageExForCompBlendLighten()` - 变亮混合压缩扩展图像绘制
20. `DrawWithImageExForCompBlendDifference()` - 差值混合压缩扩展图像绘制
21. `DrawWithImageExForCompBlendExclusion()` - 排除混合压缩扩展图像绘制

## 迁移注意事项

### 1. 错误处理
- DirectX 9.0的LockRect失败时直接返回错误码
- 不需要检查表面是否丢失（DirectX 9.0自动处理）

### 2. 性能考虑
- DirectX 9.0的表面锁定性能更好
- 减少了不必要的标志和初始化步骤

### 3. 内存对齐
- DirectX 9.0的Pitch可能与DirectX 7.0的lPitch略有不同
- 确保正确处理行间距计算

### 4. 兼容性
- 保持原有的图像处理逻辑不变
- 只替换表面锁定相关的API调用

## 批量迁移建议

1. **按功能分组**：将相似的绘制方法分组处理
2. **测试验证**：每迁移几个方法就进行编译测试
3. **保留备份**：在大规模修改前备份原始代码
4. **逐步推进**：不要一次性修改所有方法

## 常见问题

### Q: 迁移后图像显示异常？
A: 检查Pitch计算是否正确，确保使用lockedRect.Pitch而不是ddsd.lPitch

### Q: 性能是否有影响？
A: DirectX 9.0的表面锁定通常性能更好，但具体效果取决于硬件

### Q: 是否需要修改图像处理算法？
A: 不需要，只需要替换表面锁定相关的API调用即可

## 总结

表面锁定代码的迁移是DirectX 9.0迁移的重要组成部分。通过系统性地替换DDSURFACEDESC2为D3DLOCKED_RECT，可以确保图像处理功能在新的API下正常工作，同时获得更好的性能和稳定性。
