﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{fcc1d5ca-c110-4631-92af-67d937b99ea1}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;rc;def;r;odl;idl;hpj;bat</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{9f6a4ec8-da0c-4e86-b060-023c9b56d755}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl</Extensions>
    </Filter>
    <!-- DirectX 9.0 迁移：移除D3DCommon过滤器 -->
    <Filter Include="Resource Files">
      <UniqueIdentifier>{379f2044-1184-4602-ac11-6d6b85a6abad}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="RegHandler.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="StdAfx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="WHDefProcess.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="WHDXGraphic.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="WHImage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="WHSurface.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="WHWilImage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="WHWilTexture.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="WHWindow.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="WHUtils.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <!-- DirectX 9.0 迁移：移除过时的D3DCommon源文件引用 -->
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="RegHandler.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="StdAfx.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="WHDefProcess.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="WHDXGraphic.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="WHEngine.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="WHSurface.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="WHWilImage.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="WHWilTexture.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="WHWindow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="WHUtils.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <!-- DirectX 9.0 迁移：移除过时的D3DCommon头文件引用 -->
  </ItemGroup>
  <ItemGroup>
    <Text Include="Readme.txt" />
  </ItemGroup>
</Project>