# DirectX 9.0 迁移指南

## 概述

本文档详细说明了从DirectX 7.0到DirectX 9.0的迁移过程，包括主要变更、技术细节和最佳实践。

## 主要变更

### 1. 核心对象替换

| DirectX 7.0 | DirectX 9.0 | 说明 |
|-------------|-------------|------|
| `LPDIRECTDRAW7` | `LPDIRECT3D9` | 主要图形对象 |
| `LPDIRECT3D7` | `LPDIRECT3DDEVICE9` | 3D设备对象 |
| `LPDIRECTDRAWSURFACE7` | `LPDIRECT3DSURFACE9` | 表面对象 |
| `DDSURFACEDESC2` | `D3DLOCKED_RECT` | 表面锁定结构 |

### 2. 初始化流程变更

#### DirectX 7.0 初始化：
```cpp
// 创建DirectDraw对象
DirectDrawCreateEx(NULL, (VOID**)&m_pDD, IID_IDirectDraw7, NULL);

// 设置协作级别
m_pDD->SetCooperativeLevel(m_hWnd, DDSCL_NORMAL);

// 创建主表面
m_pDD->CreateSurface(&ddsd, &m_pddsFrontBuffer, NULL);

// 获取Direct3D接口
m_pDD->QueryInterface(IID_IDirect3D7, (VOID**)&m_pD3D);

// 创建3D设备
m_pD3D->CreateDevice(IID_IDirect3DHALDevice, m_pddsBackBuffer, &m_pd3dDevice);
```

#### DirectX 9.0 初始化：
```cpp
// 创建Direct3D9对象
m_pD3D = Direct3DCreate9(D3D_SDK_VERSION);

// 设置呈现参数
D3DPRESENT_PARAMETERS d3dpp;
ZeroMemory(&d3dpp, sizeof(d3dpp));
d3dpp.Windowed = TRUE;
d3dpp.SwapEffect = D3DSWAPEFFECT_DISCARD;
d3dpp.BackBufferFormat = D3DFMT_UNKNOWN;
d3dpp.hDeviceWindow = m_hWnd;

// 创建设备
m_pD3D->CreateDevice(D3DADAPTER_DEFAULT, D3DDEVTYPE_HAL, m_hWnd, 
                     D3DCREATE_SOFTWARE_VERTEXPROCESSING, &d3dpp, &m_pd3dDevice);

// 获取后缓冲表面
m_pd3dDevice->GetBackBuffer(0, 0, D3DBACKBUFFER_TYPE_MONO, &m_pBackBufferSurface);
```

### 3. 渲染管道变更

#### 清屏操作：
```cpp
// DirectX 7.0
DDBLTFX ddbltfx;
ddbltfx.dwFillColor = dwColor;
m_pddsBackBuffer->Blt(NULL, NULL, NULL, DDBLT_COLORFILL | DDBLT_WAIT, &ddbltfx);

// DirectX 9.0
D3DCOLOR d3dColor = D3DCOLOR_ARGB(0xFF, (dwColor >> 16) & 0xFF, 
                                  (dwColor >> 8) & 0xFF, dwColor & 0xFF);
m_pd3dDevice->Clear(0, NULL, D3DCLEAR_TARGET, d3dColor, 1.0f, 0);
```

#### 呈现操作：
```cpp
// DirectX 7.0
m_pddsFrontBuffer->Blt(&m_rcWindow, m_pddsBackBuffer, NULL, DDBLT_WAIT, NULL);

// DirectX 9.0
m_pd3dDevice->Present(NULL, NULL, NULL, NULL);
```

### 4. 表面锁定变更

#### DirectX 7.0：
```cpp
DDSURFACEDESC2 ddsd;
m_pddsBackBuffer->Lock(NULL, &ddsd, DDLOCK_WAIT | DDLOCK_SURFACEMEMORYPTR, NULL);
WORD* pwdDst = (WORD*)ddsd.lpSurface;
LONG lWidth = ddsd.lPitch >> 1;
m_pddsBackBuffer->Unlock(NULL);
```

#### DirectX 9.0：
```cpp
D3DLOCKED_RECT lockedRect;
m_pBackBufferSurface->LockRect(&lockedRect, NULL, 0);
WORD* pwdDst = (WORD*)lockedRect.pBits;
LONG lWidth = lockedRect.Pitch >> 1;
m_pBackBufferSurface->UnlockRect();
```

## 新增功能

### 1. 着色器支持

DirectX 9.0引入了可编程着色器管道：

```cpp
// 顶点着色器创建
LPD3DXBUFFER pVertexShaderCode;
D3DXCompileShader(vertexShaderSource, strlen(vertexShaderSource), 
                  NULL, NULL, "main", "vs_2_0", 0, &pVertexShaderCode, NULL, NULL);
m_pd3dDevice->CreateVertexShader((DWORD*)pVertexShaderCode->GetBufferPointer(), 
                                 &m_pVertexShader);

// 像素着色器创建
LPD3DXBUFFER pPixelShaderCode;
D3DXCompileShader(pixelShaderSource, strlen(pixelShaderSource), 
                  NULL, NULL, "main", "ps_2_0", 0, &pPixelShaderCode, NULL, NULL);
m_pd3dDevice->CreatePixelShader((DWORD*)pPixelShaderCode->GetBufferPointer(), 
                                &m_pPixelShader);
```

### 2. 批处理渲染

提高渲染性能的批处理系统：

```cpp
// 开始批处理
BeginBatch();

// 添加多个精灵到批处理
for (int i = 0; i < spriteCount; i++) {
    AddSpriteToBatch(sprites[i].x, sprites[i].y, sprites[i].width, 
                     sprites[i].height, sprites[i].texture);
}

// 结束批处理并渲染
EndBatch();
```

### 3. 设备丢失处理

DirectX 9.0需要处理设备丢失情况：

```cpp
// 检查设备状态
HRESULT hr = m_pd3dDevice->TestCooperativeLevel();
if (hr == D3DERR_DEVICELOST) {
    // 设备丢失，等待恢复
    return hr;
} else if (hr == D3DERR_DEVICENOTRESET) {
    // 设备可以重置
    OnLostDevice();  // 释放D3DPOOL_DEFAULT资源
    m_pd3dDevice->Reset(&m_d3dpp);  // 重置设备
    OnResetDevice(); // 重新创建资源
}
```

## 性能优化建议

### 1. 内存池选择

- **D3DPOOL_MANAGED**：适用于静态资源，自动处理设备丢失
- **D3DPOOL_DEFAULT**：适用于动态资源，性能更好但需要手动处理设备丢失

### 2. 批处理渲染

- 将相同纹理的精灵合并到一个批处理中
- 减少状态切换和Draw Call次数
- 使用顶点缓冲区和索引缓冲区

### 3. 纹理管理

- 使用纹理图集减少纹理切换
- 合理设置纹理过滤模式
- 及时释放不需要的纹理资源

## 常见问题

### Q: 编译时出现DirectX相关错误？
A: 确保安装了DirectX 9.0 SDK，并正确配置了包含路径和库路径。

### Q: 运行时设备创建失败？
A: 检查显卡驱动是否支持DirectX 9.0，尝试使用软件渲染模式。

### Q: 纹理显示异常？
A: 检查纹理格式是否支持，确保正确设置了采样器状态。

### Q: 性能比DirectX 7.0版本差？
A: 启用批处理渲染，优化Draw Call，使用硬件顶点处理。

## 测试验证

运行`Test_DirectX9_Migration.cpp`进行功能验证：

1. 设备创建测试
2. 表面操作测试
3. 着色器支持测试
4. 批处理渲染测试
5. 设备丢失处理测试

所有测试通过即表示迁移成功。

## 总结

DirectX 9.0迁移带来了以下优势：

- **更好的性能**：硬件加速和批处理渲染
- **更强的功能**：可编程着色器支持
- **更好的稳定性**：完善的设备丢失处理
- **更好的兼容性**：支持更多现代显卡

迁移过程中保持了API的向后兼容性，现有代码只需要少量修改即可适配新的图形引擎。
