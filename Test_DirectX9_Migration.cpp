/******************************************************************************************************************

	DirectX 9.0 迁移测试程序
	
	此文件用于测试DirectX 9.0迁移的基本功能
	包括设备创建、表面操作、着色器和批处理等

*******************************************************************************************************************/

#include "StdAfx.h"
#include "WindHorn/WHDXGraphic.h"

// 测试函数声明
BOOL TestDeviceCreation();
BOOL TestSurfaceOperations();
BOOL TestShaderSupport();
BOOL TestBatchRendering();
BOOL TestDeviceLostHandling();

// 全局变量
CWHDXGraphicWindow* g_pGraphics = NULL;

// 主测试函数
int TestDirectX9Migration()
{
	printf("=== DirectX 9.0 迁移测试开始 ===\n");

	// 创建图形窗口对象
	g_pGraphics = new CWHDXGraphicWindow(800, 600, 16);
	if (!g_pGraphics)
	{
		printf("错误：无法创建图形窗口对象\n");
		return -1;
	}

	// 创建窗口
	if (!g_pGraphics->Create(GetModuleHandle(NULL), TEXT("DirectX 9.0 迁移测试"), NULL, NULL, _DXG_SCREENMODE_WINDOW, _DXG_DEVICEMODE_D3D))
	{
		printf("错误：无法创建窗口\n");
		delete g_pGraphics;
		return -1;
	}

	int testResults = 0;

	// 测试1：设备创建
	printf("\n1. 测试设备创建...\n");
	if (TestDeviceCreation())
	{
		printf("   ✓ 设备创建测试通过\n");
	}
	else
	{
		printf("   ✗ 设备创建测试失败\n");
		testResults++;
	}

	// 测试2：表面操作
	printf("\n2. 测试表面操作...\n");
	if (TestSurfaceOperations())
	{
		printf("   ✓ 表面操作测试通过\n");
	}
	else
	{
		printf("   ✗ 表面操作测试失败\n");
		testResults++;
	}

	// 测试3：着色器支持
	printf("\n3. 测试着色器支持...\n");
	if (TestShaderSupport())
	{
		printf("   ✓ 着色器支持测试通过\n");
	}
	else
	{
		printf("   ✗ 着色器支持测试失败\n");
		testResults++;
	}

	// 测试4：批处理渲染
	printf("\n4. 测试批处理渲染...\n");
	if (TestBatchRendering())
	{
		printf("   ✓ 批处理渲染测试通过\n");
	}
	else
	{
		printf("   ✗ 批处理渲染测试失败\n");
		testResults++;
	}

	// 测试5：设备丢失处理
	printf("\n5. 测试设备丢失处理...\n");
	if (TestDeviceLostHandling())
	{
		printf("   ✓ 设备丢失处理测试通过\n");
	}
	else
	{
		printf("   ✗ 设备丢失处理测试失败\n");
		testResults++;
	}

	// 清理资源
	delete g_pGraphics;
	g_pGraphics = NULL;

	// 输出测试结果
	printf("\n=== DirectX 9.0 迁移测试完成 ===\n");
	if (testResults == 0)
	{
		printf("所有测试通过！DirectX 9.0 迁移成功。\n");
		return 0;
	}
	else
	{
		printf("有 %d 个测试失败。需要进一步调试。\n", testResults);
		return testResults;
	}
}

// 测试设备创建
BOOL TestDeviceCreation()
{
	if (!g_pGraphics)
		return FALSE;

	// 检查Direct3D9对象是否创建成功
	LPDIRECT3D9 pD3D = g_pGraphics->GetDirect3D();
	if (!pD3D)
	{
		printf("   错误：Direct3D9对象未创建\n");
		return FALSE;
	}

	// 检查Direct3D9设备是否创建成功
	LPDIRECT3DDEVICE9 pDevice = g_pGraphics->Get3DDevice();
	if (!pDevice)
	{
		printf("   错误：Direct3D9设备未创建\n");
		return FALSE;
	}

	// 检查后缓冲表面是否获取成功
	LPDIRECT3DSURFACE9 pBackBuffer = g_pGraphics->GetBackBufferSurface();
	if (!pBackBuffer)
	{
		printf("   错误：后缓冲表面未获取\n");
		return FALSE;
	}

	printf("   Direct3D9对象、设备和后缓冲表面创建成功\n");
	return TRUE;
}

// 测试表面操作
BOOL TestSurfaceOperations()
{
	if (!g_pGraphics)
		return FALSE;

	// 测试Clear操作
	HRESULT hr = g_pGraphics->Clear(0x00FF0000); // 红色
	if (FAILED(hr))
	{
		printf("   错误：Clear操作失败 (HRESULT: 0x%08X)\n", hr);
		return FALSE;
	}

	// 测试Present操作
	hr = g_pGraphics->Present();
	if (FAILED(hr))
	{
		printf("   错误：Present操作失败 (HRESULT: 0x%08X)\n", hr);
		return FALSE;
	}

	printf("   Clear和Present操作成功\n");
	return TRUE;
}

// 测试着色器支持
BOOL TestShaderSupport()
{
	if (!g_pGraphics)
		return FALSE;

	// 测试着色器创建
	HRESULT hr = g_pGraphics->CreateShaders();
	if (FAILED(hr))
	{
		printf("   错误：着色器创建失败 (HRESULT: 0x%08X)\n", hr);
		return FALSE;
	}

	// 测试顶点缓冲区创建
	hr = g_pGraphics->CreateVertexBuffer();
	if (FAILED(hr))
	{
		printf("   错误：顶点缓冲区创建失败 (HRESULT: 0x%08X)\n", hr);
		return FALSE;
	}

	// 测试索引缓冲区创建
	hr = g_pGraphics->CreateIndexBuffer();
	if (FAILED(hr))
	{
		printf("   错误：索引缓冲区创建失败 (HRESULT: 0x%08X)\n", hr);
		return FALSE;
	}

	printf("   着色器、顶点缓冲区和索引缓冲区创建成功\n");
	return TRUE;
}

// 测试批处理渲染
BOOL TestBatchRendering()
{
	if (!g_pGraphics)
		return FALSE;

	// 测试批处理缓冲区创建
	HRESULT hr = g_pGraphics->CreateBatchBuffers();
	if (FAILED(hr))
	{
		printf("   错误：批处理缓冲区创建失败 (HRESULT: 0x%08X)\n", hr);
		return FALSE;
	}

	// 测试批处理操作
	hr = g_pGraphics->BeginBatch();
	if (FAILED(hr))
	{
		printf("   错误：BeginBatch失败 (HRESULT: 0x%08X)\n", hr);
		return FALSE;
	}

	hr = g_pGraphics->EndBatch();
	if (FAILED(hr))
	{
		printf("   错误：EndBatch失败 (HRESULT: 0x%08X)\n", hr);
		return FALSE;
	}

	printf("   批处理缓冲区和操作成功\n");
	return TRUE;
}

// 测试设备丢失处理
BOOL TestDeviceLostHandling()
{
	if (!g_pGraphics)
		return FALSE;

	// 测试设备状态检查
	BOOL bDeviceLost = g_pGraphics->IsDeviceLost();
	printf("   当前设备状态：%s\n", bDeviceLost ? "丢失" : "正常");

	// 测试设备丢失处理方法
	HRESULT hr = g_pGraphics->OnLostDevice();
	if (FAILED(hr))
	{
		printf("   错误：OnLostDevice失败 (HRESULT: 0x%08X)\n", hr);
		return FALSE;
	}

	hr = g_pGraphics->OnResetDevice();
	if (FAILED(hr))
	{
		printf("   错误：OnResetDevice失败 (HRESULT: 0x%08X)\n", hr);
		return FALSE;
	}

	printf("   设备丢失处理方法测试成功\n");
	return TRUE;
}
