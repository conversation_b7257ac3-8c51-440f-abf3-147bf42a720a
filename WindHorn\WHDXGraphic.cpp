/******************************************************************************************************************

	ģ����:

	����:
	��������:

	[����][�޸���] : �޸�����

*******************************************************************************************************************/


#include "stdafx.h"
#include "WHEngine.h"
#include <windows.h>


/*********************************************************************************************

	// ORZ: Alpha��Ϻ�

 *********************************************************************************************/
#define ALPHA_565_MASK1		0xf81f07e0
#define ALPHA_565_MASK2		0x07e0f81f


 /*********************************************************************************************

	 // ORZ: ��Ϻ꺯������

	 - Դ�����࣡

  *********************************************************************************************/
#define IS_OUTSIDE( nX, nY, nXSize, nYSize )\
	( nX > m_stDisplayInfo.wWidth || nX + nXSize < 0 || nY > m_stDisplayInfo.wHeight || nY + nYSize < 0 )

#define CREATE_CLIP_REGION( nX, nY, nXSize, nYSize )\
	if ( nX + nXSize > m_stDisplayInfo.wWidth )	{ rc.right	= m_stDisplayInfo.wWidth -nX; }\
	else						{ rc.right	= nXSize; }\
	if ( nX < 0 )				{ rc.left	= -nX; nX = 0; }\
	else						{ rc.left	= 0; }\
	if ( nY + nYSize > m_stDisplayInfo.wHeight )	{ rc.bottom	= m_stDisplayInfo.wHeight - nY; }\
	else						{ rc.bottom	= nYSize; }\
	if ( nY < 0 )				{ rc.top	= -nY; nY = 0; }\
	else						{ rc.top	= 0; }\
\
	imgW = rc.right - rc.left;\
	imgH = rc.bottom - rc.top;





  /*********************************************************************************************

	  // ORZ: �����������

   *********************************************************************************************/
__forceinline void putAlpha32Pixel(WORD* dst, WORD src, byte srclevel)
{
	static DWORD src1, src2, dst1, dst2;

	int dstlevel = 32 - srclevel;

	src1 = src;
	src2 = src1;

	src1 &= ALPHA_565_MASK1;
	src1 >>= 5;
	src1 *= srclevel;
	src1 &= ALPHA_565_MASK1;

	src2 &= ALPHA_565_MASK2;
	src2 *= srclevel;
	src2 >>= 5;
	src2 &= ALPHA_565_MASK2;
	src2 |= src1;

	dst1 = *dst;
	dst2 = dst1;

	dst1 &= ALPHA_565_MASK1;
	dst1 >>= 5;
	dst1 *= dstlevel;
	dst1 &= ALPHA_565_MASK1;

	dst2 &= ALPHA_565_MASK2;
	dst2 *= dstlevel;
	dst2 >>= 5;
	dst2 &= ALPHA_565_MASK2;
	dst2 |= dst1;

	*dst++ = (WORD)(dst2 + src2);
}


__forceinline void putAlpha32(WORD* dst, WORD* src, int len, byte srclevel)
{
	static DWORD src1, src2, dst1, dst2;

	int dstlevel = 32 - srclevel;

	if (len & 1)
	{
		src1 = *src++;
		src2 = src1;

		src1 &= ALPHA_565_MASK1;
		src1 >>= 5;
		src1 *= srclevel;
		src1 &= ALPHA_565_MASK1;

		src2 &= ALPHA_565_MASK2;
		src2 *= srclevel;
		src2 >>= 5;
		src2 &= ALPHA_565_MASK2;
		src2 |= src1;

		dst1 = *dst;
		dst2 = dst1;

		dst1 &= ALPHA_565_MASK1;
		dst1 >>= 5;
		dst1 *= dstlevel;
		dst1 &= ALPHA_565_MASK1;

		dst2 &= ALPHA_565_MASK2;
		dst2 *= dstlevel;
		dst2 >>= 5;
		dst2 &= ALPHA_565_MASK2;
		dst2 |= dst1;

		*dst++ = (WORD)(dst2 + src2);
	}

	for (int i = 0; i < len >> 1; i++)
	{
		src1 = *((DWORD*)src);
		src2 = src1;

		src1 &= ALPHA_565_MASK1;
		src1 >>= 5;
		src1 *= srclevel;
		src1 &= ALPHA_565_MASK1;

		src2 &= ALPHA_565_MASK2;
		src2 *= srclevel;
		src2 >>= 5;
		src2 &= ALPHA_565_MASK2;
		src2 |= src1;

		dst1 = *((DWORD*)dst);
		dst2 = dst1;

		dst1 &= ALPHA_565_MASK1;
		dst1 >>= 5;
		dst1 *= dstlevel;
		dst1 &= ALPHA_565_MASK1;

		dst2 &= ALPHA_565_MASK2;
		dst2 *= dstlevel;
		dst2 >>= 5;
		dst2 &= ALPHA_565_MASK2;
		dst2 |= dst1;

		*((DWORD*)dst) = dst2 + src2;

		dst += 2;
		src += 2;
	}
}


/******************************************************************************************************************

	CWHDXGraphicWindow Functions

*******************************************************************************************************************/


static BYTE						g_bNumDevices = 0;
static DXG_ENUM_DEVICEINFO		g_stDXGEnumDeviceInfo[_MAX_DEVICES];


CWHDXGraphicWindow* CWHDXGraphicWindow::m_pxDXGWnd = NULL;

CWHDXGraphicWindow* GetDXGWindowPtr()
{
	return CWHDXGraphicWindow::m_pxDXGWnd;
}


/******************************************************************************************************************

	������ : CWHDXGraphicWindow::CWHDXGraphicWindow()

	���� :
	�������� :

	Ŀ�� :
	���� : WORD wWidth
			 WORD wHeight
			 WORD wBpp
	��� :

	[����][�޸���] : �޸�����

*******************************************************************************************************************/
// DirectX 9.0 Ǩ�ƣ����¹��캯��
CWHDXGraphicWindow::CWHDXGraphicWindow(WORD wWidth, WORD wHeight, WORD wBpp)
{
	// DirectX 9.0 Ǩ�ƣ���ʼ���µĳ�Ա����
	m_pD3D = NULL;
	m_hDefGameFont = NULL;

	m_pd3dDevice = NULL;
	m_pddsOffscreenSurface = NULL;
	m_pddsBackBuffer = NULL;
	m_pDepthStencilSurface = NULL;

	// DirectX 9.0 Ǩ�ƣ���ʼ����Դ��������
	ZeroMemory(&m_d3dpp, sizeof(m_d3dpp));

	// DirectX 9.0 Ǩ�ƣ���ʼ����ɫ����ر���
	m_pVertexShader = NULL;
	m_pPixelShader = NULL;
	m_pVertexBuffer = NULL;
	m_pIndexBuffer = NULL;
	m_pVertexDecl = NULL;

	// DirectX 9.0 Ǩ�ƣ���ʼ����������ر���
	m_pBatchVertexBuffer = NULL;
	m_pBatchIndexBuffer = NULL;
	m_dwMaxBatchSize = 0;
	m_dwCurrentBatchSize = 0;
	m_pCurrentTexture = NULL;

	//	Ϊ�༭����������òü�����
		// DirectX 9.0 Ǩ�ƣ��Ƴ��ĳ�Ա����������ע�ͣ�
		// m_pDD				= NULL;  // ���Ƴ�
		// m_pddsZBuffer		= NULL;  // �滻Ϊm_pDepthStencilSurface
		// m_lpcClipper		= NULL;  // ���Ƴ���ʹ���ӿڲü�

	ZeroMemory(m_pszDefFont, MAX_PATH);

	m_stDisplayInfo.wWidth = wWidth;
	m_stDisplayInfo.wHeight = wHeight;
	m_stDisplayInfo.wBPP = wBpp;
	m_bScreenModeFlag = _DXG_SCREENMODE_WINDOW;
	m_bDeviceModeFlag = _DXG_DEVICEMODE_PRIMARY;

	SetRect(&m_rcWindow, 0, 0, 0, 0);
	ZeroMemory(&m_stBitsMaskInfo, sizeof(DXG_MASKINFO));
	ZeroMemory(&m_stSurfDesc, sizeof(D3DSURFACE_DESC));

	m_pxDXGWnd = this;

	m_bIsWindowActive = FALSE;
	m_bIsWindowReady = FALSE;
	m_bIsActiveChanged = FALSE;

	m_dwTextureTotal = 0;
	m_dwTextureFree = 0;
	m_dwVideoTotal = 0;
	m_dwVideoFree = 0;

	m_pxDefProcess = NULL;
}


// DirectX 9.0 Ǩ�ƣ��ڴ�ʹ��������
VOID CWHDXGraphicWindow::UsedAndFreeMemoryCheck()
{
	// DirectX 9.0 Ǩ�ƣ�ʹ��Direct3D9�ӿڻ�ȡ�ڴ���Ϣ
	if (m_pd3dDevice)
	{
		// ��ȡ��������Ϣ
		UINT uAdapter = D3DADAPTER_DEFAULT;
		if (m_pD3D)
		{
			// �����Դ��С����ʵ�֣�
			m_dwVideoTotal = m_pD3D->GetAdapterModeCount(uAdapter, D3DFMT_X8R8G8B8) * 1024 * 1024; // ����ֵ
			m_dwVideoFree = m_dwVideoTotal / 2; // ����һ�����

			// �����ڴ����Դ���ͬ��DirectX 9.0�й���Դ��
			m_dwTextureTotal = m_dwVideoTotal;
			m_dwTextureFree = m_dwVideoFree;
		}
	}
	else
	{
		// �豸δ��ʼ��ʱ����
		m_dwVideoTotal = m_dwVideoFree = 0;
		m_dwTextureTotal = m_dwTextureFree = 0;
	}
}


/******************************************************************************************************************

	������ : CWHDXGraphicWindow::~CWHDXGraphicWindow()

	���� :
	�������� :

	Ŀ�� :
	��� :

	[����][�޸���] : �޸�����

*******************************************************************************************************************/
CWHDXGraphicWindow::~CWHDXGraphicWindow()
{
	FreeDXGEnumModeResources();
	DestroyDXGObjects();

	m_pxDefProcess = NULL;
	m_pxDXGWnd = NULL;
}


/******************************************************************************************************************

	������ : CWHDXGraphicWindow::Create()

	���� :
	�������� :

	Ŀ�� :
	���� : LPTSTR lpCaption
			 CHAR *pszMenuName
			 BYTE bScreenModeFlag
			 BYTE bDeviceModeFlag
	��� : BOOL

	[����][�޸���] : �޸�����

*******************************************************************************************************************/
BOOL CWHDXGraphicWindow::Create(HINSTANCE hInst, LPTSTR lpCaption, CHAR* pszMenuName, CHAR* pszIconName, BYTE bScreenModeFlag, BYTE bDeviceModeFlag)
{
	DWORD	dwStyle;

	if (!CWHWindow::Create(hInst, lpCaption, pszMenuName, pszIconName))
	{
		MessageBox(m_hWnd, TEXT("[CWHDXGraphicWindow::Create]") TEXT("Window create failed."), "MirDXG", MB_ICONERROR | MB_OK);
		return FALSE;
	}

	m_bScreenModeFlag = bScreenModeFlag;
	m_bDeviceModeFlag = bDeviceModeFlag;

	if (m_bScreenModeFlag & _DXG_SCREENMODE_WINDOW)
	{
		dwStyle = GetWindowStyle(m_hWnd);
		dwStyle &= ~WS_POPUP;
		dwStyle |= WS_OVERLAPPED | WS_CAPTION | WS_MINIMIZEBOX;
		SetWindowLong(m_hWnd, GWL_STYLE, dwStyle);
		//		ShowWindow(m_hWnd, SW_HIDE);
		//		SetWindowLongPtr(m_hWnd, GWL_STYLE, dwStyle);
	}
	else
	{
		dwStyle = GetWindowStyle(m_hWnd);
		dwStyle &= ~dwStyle;
		dwStyle |= WS_POPUP | WS_SYSMENU;
		SetWindowLong(m_hWnd, GWL_STYLE, dwStyle);
		ShowWindow(m_hWnd, SW_HIDE);
		//		SetWindowLongPtr(m_hWnd, GWL_STYLE, dwStyle);
	}

	// ö��DirectX 9.0������
	if (SUCCEEDED(EnumerateD3D9Adapters()))
	{
		if (SUCCEEDED(CreateDXG()))
		{
			m_bIsWindowReady = TRUE;
			SetDefFont();
			return TRUE;
		}
		else
		{
			MessageBox(m_hWnd, TEXT("[CWHDXGraphicWindow::Create]") TEXT("DirectGraphic create failed."), "MirDXG", MB_ICONERROR | MB_OK);
			return FALSE;
		}
	}

	return FALSE;
}


/******************************************************************************************************************

	������ : CWHDXGraphicWindow::MainWndProcDXG()

	���� :
	�������� :

	Ŀ�� :
	���� : HWND hWnd
			 UINT uMsg
			 WPARAM wParam
			 LPARAM lParam
	��� : LRESULT

	[����][�޸���] : �޸�����

*******************************************************************************************************************/
LRESULT CWHDXGraphicWindow::MainWndProcDXG(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam)
{
	switch (uMsg)
	{
	case WM_CREATE:
	{
		RegisterHotKey(hWnd, 0, MOD_ALT, VK_F4);
		//			RegisterHotKey( hWnd, 1, MOD_ALT, VK_TAB );
		break;
	}

	case WM_HOTKEY:
	{
		switch (wParam)
		{
		case 0:		// F4
			SendMessage(hWnd, WM_DESTROY, 0, 0);
			break;
			//			case 1:		// TAB
			//				break;
		}
		break;
	}

	case WM_SYSKEYDOWN:
		return OnSysKeyDown(wParam, lParam);

	case WM_MOVE:
		return OnMove(wParam, lParam);

	case WM_SIZE:
		return OnSize(wParam, lParam);

	case WM_DESTROY:
	{
		UnregisterHotKey(hWnd, 0);
		UnregisterHotKey(hWnd, 1);
		return OnDestroy();
	}

	case WM_SETCURSOR:
		return OnSetCursor();

		//      case WM_GETMINMAXINFO:			
	   //			return OnGetMinMaxInfo(wParam, lParam);

	default:
		break;
	}

	return CWHWindow::MainWndProc(hWnd, uMsg, wParam, lParam);
}


LRESULT CWHDXGraphicWindow::OnGetMinMaxInfo(WPARAM wParam, LPARAM lParam)
{
	if (m_hWnd)
	{
		MINMAXINFO* pMinMax = (MINMAXINFO*)lParam;

		DWORD dwFrameWidth = GetSystemMetrics(SM_CXSIZEFRAME);
		DWORD dwFrameHeight = GetSystemMetrics(SM_CYSIZEFRAME);
		DWORD dwMenuHeight;
		if (GetMenu(m_hWnd))
			dwMenuHeight = GetSystemMetrics(SM_CYMENU);
		else
			dwMenuHeight = 0;

		DWORD dwCaptionHeight = GetSystemMetrics(SM_CYCAPTION);

		pMinMax->ptMinTrackSize.x = m_stDisplayInfo.wWidth + dwFrameWidth * 2;
		pMinMax->ptMinTrackSize.y = m_stDisplayInfo.wHeight + dwFrameHeight * 2 +
			dwMenuHeight + dwCaptionHeight;

		pMinMax->ptMaxTrackSize.x = pMinMax->ptMinTrackSize.x;
		pMinMax->ptMaxTrackSize.y = pMinMax->ptMinTrackSize.y;
	}

	return 0L;
}


LRESULT CWHDXGraphicWindow::OnSetCursor()
{
	SetCursor(LoadCursor(NULL, IDC_ARROW));
	return 0L;
}



/******************************************************************************************************************

	������ : CWHDXGraphicWindow::OnSize()

	���� :
	�������� :

	Ŀ�� :
	���� : WPARAM wParam
			 LPARAM lParam
	��� : LRESULT

	[����][�޸���] : �޸�����

*******************************************************************************************************************/
LRESULT CWHDXGraphicWindow::OnSize(WPARAM wParam, LPARAM lParam)
{
	if (SIZE_MAXHIDE == wParam || SIZE_MINIMIZED == wParam)
		m_bIsWindowActive = FALSE;
	else
	{
		if (m_bIsWindowActive == FALSE)
		{
			m_bIsActiveChanged = TRUE;
		}
		m_bIsWindowActive = TRUE;
	}

	UpdateBoundsWnd();
	return 0L;
}



/******************************************************************************************************************

	������ : CWHDXGraphicWindow::OnMove()

	���� :
	�������� :

	Ŀ�� :
	���� : WPARAM wParam
			 LPARAM lParam
	��� : LRESULT

	[����][�޸���] : �޸�����

*******************************************************************************************************************/
LRESULT CWHDXGraphicWindow::OnMove(WPARAM wParam, LPARAM lParam)
{
	UpdateBoundsWnd();
	return 0L;
}


/******************************************************************************************************************

	������ : CWHDXGraphicWindow::OnDestroy()

	���� :
	�������� :

	Ŀ�� :
	��� : BOOL

	[����][�޸���] : �޸�����

*******************************************************************************************************************/
LRESULT CWHDXGraphicWindow::OnDestroy()
{
	return CWHWindow::OnDestroy();
}


/******************************************************************************************************************

	������ : CWHDXGraphicWindow::OnSysKeyDown()

	���� :
	�������� :

	Ŀ�� :
	���� : WPARAM wParam
			 LPARAM lParam
	��� : LRESULT

	[����][�޸���] : �޸�����

*******************************************************************************************************************/
LRESULT CWHDXGraphicWindow::OnSysKeyDown(WPARAM wParam, LPARAM lParam)
{
	if (wParam == VK_RETURN)
	{
		m_bIsWindowReady = FALSE;

		if (m_bScreenModeFlag & _DXG_SCREENMODE_WINDOW)
		{
			if (FAILED(ResetDXG(_SCREEN_WIDTH, _SCREEN_HEIGHT, _SCREEN_BPP, _DXG_SCREENMODE_FULLSCREEN, _DXG_DEVICEMODE_PRIMARY | _DXG_DEVICEMODE_D3D)))
			{
				MessageBox(m_hWnd, TEXT("DirectDraw RESETUP failed!!!") TEXT("The Program will now exit."), TEXT("MirDXG"), MB_ICONERROR | MB_OK);
			}
		}
		else
		{
			if (FAILED(ResetDXG(_SCREEN_WIDTH, _SCREEN_HEIGHT, _SCREEN_BPP, _DXG_SCREENMODE_WINDOW, _DXG_DEVICEMODE_PRIMARY | _DXG_DEVICEMODE_D3D)))
			{
				MessageBox(m_hWnd, TEXT("DirectDraw RESETUP failed!!!") TEXT("The Program will now exit."), TEXT("MirDXG"), MB_ICONERROR | MB_OK);
			}
		}

		m_bIsWindowReady = TRUE;
	}

	return 0L;
}


/******************************************************************************************************************

	������ : CWHDXGraphicWindow::FindDriverAndDevice()

	���� :
	�������� :

	Ŀ�� :
	��� : DWORD

	[����][�޸���] : �޸�����

*******************************************************************************************************************/
BYTE CWHDXGraphicWindow::FindDriverAndDevice()
{
	BYTE bNum = 0;
	CHAR bCnt;

	// DirectX 9.0 Ǩ�ƣ����豸ѡ���߼�
	if (m_bDeviceModeFlag & _DXG_DEVICEMODE_PRIMARY)
	{
		// ����ѡ������ʾ������
		for (bCnt = 0; bCnt < g_bNumDevices; bCnt++)
		{
			if (g_stDXGEnumDeviceInfo[bCnt].uAdapter == 0)  // ��������
			{
				if (g_stDXGEnumDeviceInfo[bCnt].f3DHardware)
					return bCnt;
				else
					bNum = bCnt;
			}
		}
	}
	else
	{
		// ѡ����ѿ����豸
		for (bCnt = g_bNumDevices - 1; bCnt >= 0; bCnt--)
		{
			if (g_stDXGEnumDeviceInfo[bCnt].f3DHardware)
				return bCnt;
			else
				bNum = bCnt;
		}
	}

	return bNum;
}


/******************************************************************************************************************

	������ : CWHDXGraphicWindow::DestroyDXGObjects()

	���� :
	�������� :

	Ŀ�� :
	��� : HRESULT

	[����][�޸���] : �޸�����

*******************************************************************************************************************/
HRESULT CWHDXGraphicWindow::DestroyDXGObjects()
{
	//	HRESULT	hr;
		// DirectX 9.0 Ǩ�ƣ����¶����ͷŷ���
	LONG	lD3DRefCnt = 0L;

	// DirectX 9.0 Ǩ�ƣ�����Ҫ�ָ���ʾģʽ��Direct3D9�Զ�����
	// if ( !(m_bScreenModeFlag & _DXG_SCREENMODE_WINDOW) )
	// {
	//     m_pDD->RestoreDisplayMode(); // ���Ƴ�
	// }

	// �ͷ�Direct3D9�豸
	if (m_pd3dDevice)
	{
		m_pd3dDevice->Release();
		//        if ( 0 < ( lD3DRefCnt = m_pd3dDevice->Release() ) )
		//			return E_FAIL;
	}
	m_pd3dDevice = NULL;

	// DirectX 9.0 Ǩ�ƣ��ͷ��µı������������
	SAFE_RELEASE(m_pddsOffscreenSurface);
	SAFE_RELEASE(m_pddsBackBuffer);
	SAFE_RELEASE(m_pDepthStencilSurface);

	// DirectX 9.0 Ǩ�ƣ��ͷ���ɫ����Դ
	ReleaseShaders();

	SAFE_RELEASE(m_pD3D);

	// DirectX 9.0 Ǩ�ƣ��Ƴ��Ķ��󣨱���ע�ͣ�
	// SAFE_RELEASE(m_pddsZBuffer);      // �滻Ϊm_pDepthStencilSurface
	// SAFE_RELEASE(m_lpcClipper);       // ���Ƴ�
	// SAFE_RELEASE(m_pDD);              // ���Ƴ�

	if (m_hDefGameFont != NULL)
	{
		DeleteObject(m_hDefGameFont);
		m_hDefGameFont = NULL;
		//		ZeroMemory(m_pszDefFont, MAX_PATH);
	}

	return S_OK;
	//    return ( lDDRefCnt==0 && lD3DRefCnt==0 ) ? S_OK : E_FAIL;
}


/******************************************************************************************************************

	������ : CWHDXGraphicWindow::FreeDXGEnumModeResources()

	���� :
	�������� :

	Ŀ�� :
	��� : VOID

	[����][�޸���] : �޸�����

*******************************************************************************************************************/
VOID CWHDXGraphicWindow::FreeDXGEnumModeResources()
{
	for (BYTE bCnt = 0; bCnt < g_bNumDevices; bCnt++)
	{
		// DirectX 9.0 Ǩ�ƣ�ʹ��pDisplayModes���pddsdModes
		SAFE_DELETE(g_stDXGEnumDeviceInfo[bCnt].pDisplayModes);
	}
}

// DirectX 9.0 Ǩ�ƣ���дCreateDXG����
HRESULT CWHDXGraphicWindow::CreateDXG()
{
	HRESULT hr;

	// DirectX 9.0 Ǩ�ƣ�����Direct3D9����
	m_pD3D = Direct3DCreate9(D3D_SDK_VERSION);
	if (!m_pD3D)
	{
		MessageBox(m_hWnd, TEXT("Failed to create Direct3D9 object."), TEXT("Mir3EI"), MB_ICONERROR | MB_OK);
		return E_FAIL;
	}

	// DirectX 9.0 Ǩ�ƣ����Ӳ������֧��
	D3DCAPS9 d3dCaps;
	UINT uAdapter = D3DADAPTER_DEFAULT;
	if (FAILED(hr = m_pD3D->GetDeviceCaps(uAdapter, D3DDEVTYPE_HAL, &d3dCaps)))
	{
		MessageBox(m_hWnd, TEXT("Cannot get device capabilities."), TEXT("Mir3EI"), MB_ICONINFORMATION | MB_OK);
		// ����ʹ��������Ⱦ
	}

	// DirectX 9.0 Ǩ�ƣ����ó��ֲ����������Ա�����ʹ��
	ZeroMemory(&m_d3dpp, sizeof(m_d3dpp));

	if (m_bScreenModeFlag & _DXG_SCREENMODE_WINDOW)
	{
		// ����ģʽ
		m_d3dpp.Windowed = TRUE;
		m_d3dpp.SwapEffect = D3DSWAPEFFECT_DISCARD;				// �Ƽ��Ľ���Ч������Ч���ʺϴ����������
		m_d3dpp.BackBufferCount = 1;							// ��̨����������˫������Ϊ1��
		m_d3dpp.PresentationInterval = D3DPRESENT_INTERVAL_ONE;	// ��ֱͬ������ѡ��
		m_d3dpp.BackBufferFormat = D3DFMT_UNKNOWN;	// ʹ�������ʽ
	}
	else
	{
		// ȫ��ģʽ
		m_d3dpp.Windowed = FALSE;
		m_d3dpp.SwapEffect = D3DSWAPEFFECT_DISCARD;				// �Ƽ��Ľ���Ч������Ч���ʺϴ����������
		m_d3dpp.BackBufferCount = 1;							// ��̨����������˫������Ϊ1��
		m_d3dpp.PresentationInterval = D3DPRESENT_INTERVAL_ONE;	// ��ֱͬ������ѡ��
		m_d3dpp.BackBufferWidth = m_stDisplayInfo.wWidth;
		m_d3dpp.BackBufferHeight = m_stDisplayInfo.wHeight;
		m_d3dpp.BackBufferFormat = (m_stDisplayInfo.wBPP == 16) ? D3DFMT_A1R5G5B5 : D3DFMT_X8R8G8B8;
		m_d3dpp.FullScreen_RefreshRateInHz = m_stDisplayInfo.nRefreshRate;
	}

	m_d3dpp.hDeviceWindow = m_hWnd;
	m_d3dpp.BackBufferCount = 1;

	// DirectX 9.0 Ǩ�ƣ�������Ȼ���
	if (m_bDeviceModeFlag & _DXG_DEVICEMODE_ZBUFFER)
	{
		m_d3dpp.EnableAutoDepthStencil = TRUE;
		m_d3dpp.AutoDepthStencilFormat = D3DFMT_D16; // 16λ��Ȼ���
	}

	// DirectX 9.0 Ǩ�ƣ������豸
	DWORD dwBehaviorFlags = D3DCREATE_SOFTWARE_VERTEXPROCESSING;
	if (d3dCaps.DevCaps & D3DDEVCAPS_HWTRANSFORMANDLIGHT)
	{
		dwBehaviorFlags = D3DCREATE_HARDWARE_VERTEXPROCESSING;
	}

	hr = m_pD3D->CreateDevice(uAdapter, D3DDEVTYPE_HAL, m_hWnd, dwBehaviorFlags, &m_d3dpp, &m_pd3dDevice);
	if (FAILED(hr))
	{
		// ����ʹ�������豸
		hr = m_pD3D->CreateDevice(uAdapter, D3DDEVTYPE_REF, m_hWnd, D3DCREATE_SOFTWARE_VERTEXPROCESSING, &m_d3dpp, &m_pd3dDevice);
		if (FAILED(hr))
		{
			MessageBox(m_hWnd, TEXT("Failed to create Direct3D9 device."), TEXT("MirDXG"), MB_ICONERROR | MB_OK);
			return E_FAIL;
		}
	}

	// DirectX 9.0 Ǩ�ƣ���ȡ�󻺳����
	if (FAILED(hr = m_pd3dDevice->GetBackBuffer(0, 0, D3DBACKBUFFER_TYPE_MONO, &m_pddsBackBuffer)))
	{
		MessageBox(m_hWnd, TEXT("Failed to get back buffer surface."), TEXT("MirDXG"), MB_ICONERROR | MB_OK);
		return E_FAIL;
	}

	UpdateBoundsWnd();

	// DirectX 9.0 Ǩ�ƣ���ȡ��̨�����������Ϣ
	D3DSURFACE_DESC backBufferDesc;
	if (FAILED(hr = m_pddsBackBuffer->GetDesc(&backBufferDesc)))
	{
		MessageBox(m_hWnd, TEXT("Failed to get back buffer description."), TEXT("MirDXG"), MB_ICONERROR | MB_OK);
		return E_FAIL;
	}

	// ������̨�����������Ϣ��Ա����
	m_stSurfDesc = backBufferDesc;

	// DirectX 9.0 Ǩ�ƣ���ȡRGB������Ϣ
	m_stBitsMaskInfo = GetRGBMaskInfoIN16Bits(m_pddsBackBuffer);

	// �ͷ����еĺ�̨��������
	SAFE_RELEASE(m_pddsOffscreenSurface);

	// �������̨���������ͳ��С������
	// ʹ��D3DPOOL_DEFAULT��ȷ����̨�����ݱ�
	hr = m_pd3dDevice->CreateRenderTarget(
		backBufferDesc.Width,
		backBufferDesc.Height,
		backBufferDesc.Format,
		D3DMULTISAMPLE_NONE,    // ���ض��ز���
		0,                      // ���ض��ز�������
		TRUE,                   // ���������
		&m_pddsOffscreenSurface,
		NULL);

	// DirectX 9.0 Ǩ�ƣ�������̨��������
	if (FAILED(hr))
	{
		CHAR szError[256];
		wsprintf(szError, TEXT("Failed to create lockable render surface. HRESULT: 0x%08X"), hr);
		MessageBox(m_hWnd, szError, TEXT("MirDXG"), MB_ICONERROR | MB_OK);
		return hr;
	}
	D3DLOCKED_RECT testLock;
	HRESULT testHr = m_pddsOffscreenSurface->LockRect(&testLock, NULL, D3DLOCK_READONLY);
	if (SUCCEEDED(testHr))
	{
		m_pddsOffscreenSurface->UnlockRect();
		OutputDebugString(TEXT("Lockable render surface created successfully and verified."));
	}
	else
	{
		CHAR szError[256];
		sprintf(szError, TEXT("Render surface created but cannot be locked. HRESULT: 0x%08X"), testHr);
		OutputDebugString(szError);
	}

	// ��ʼ��3D�豸����
	Init3DDeviceObjects();

	return S_OK;
}


/******************************************************************************************************************

	������ : CDXGraphic::ResetDXG()

	���� :
	�������� :

	Ŀ�� :
	���� : WORD wWidth
			 WORD wHeight
			 WORD wBPP
			 BYTE bScreenModeFlag
			 BYTE bDeviceModeFlag
	��� : HRESULT

	[����][�޸���] : �޸�����

*******************************************************************************************************************/
HRESULT CWHDXGraphicWindow::ResetDXG(WORD wWidth, WORD wHeight, WORD wBPP, BYTE bScreenModeFlag, BYTE bDeviceModeFlag)
{
	HRESULT hr;
	DWORD	dwStyle;

	//	if ( m_bDeviceModeFlag == bDeviceModeFlag && m_bScreenModeFlag == bScreenModeFlag && 
	//		 m_stDisplayInfo.wWidth == wWidth && m_stDisplayInfo.wHeight == wHeight && m_stDisplayInfo.wBPP == wBPP )
	//		return E_FAIL;													    

	if (FAILED(DestroyDXGObjects()))
	{
		MessageBox(m_hWnd, TEXT("[CWHDXGraphicWindow::ResetDXG]") TEXT("DirectGraphic reset failed."), "MirDXG", MB_ICONERROR | MB_OK);
		return E_FAIL;
	}

	m_stDisplayInfo.wWidth = wWidth;
	m_stDisplayInfo.wHeight = wHeight;
	m_stDisplayInfo.wBPP = wBPP;
	m_bScreenModeFlag = bScreenModeFlag;
	m_bDeviceModeFlag = bDeviceModeFlag;

	if (m_bScreenModeFlag & _DXG_SCREENMODE_WINDOW)
	{
		dwStyle = GetWindowStyle(m_hWnd);
		dwStyle &= ~WS_POPUP;
		dwStyle |= WS_OVERLAPPED | WS_CAPTION | WS_MINIMIZEBOX;
		SetWindowLong(m_hWnd, GWL_STYLE, dwStyle);
	}
	else
	{
		dwStyle = GetWindowStyle(m_hWnd);
		dwStyle &= ~dwStyle;
		dwStyle |= WS_POPUP | WS_SYSMENU;
		SetWindowLong(m_hWnd, GWL_STYLE, dwStyle);
	}

	if (hr = FAILED(CreateDXG()))
	{
		MessageBox(m_hWnd, TEXT("[CWHDXGraphicWindow::ResetDXG]") TEXT("DirectGraphic create failed."), "MirDXG", MB_ICONERROR | MB_OK);
		return E_FAIL;
	}

	SetDefFont();

	return S_OK;
}


// DirectX 9.0 Ǩ�ƣ�����Clear����
HRESULT CWHDXGraphicWindow::Clear(DWORD dwColor)
{
	HRESULT hr;

	if (NULL == m_pd3dDevice)
	{
		return E_POINTER;
	}

	// DirectX 9.0 Ǩ�ƣ�ʹ��Direct3D9��Clear����
	// ��DWORD��ɫת��ΪD3DCOLOR��ʽ
	D3DCOLOR d3dColor = D3DCOLOR_ARGB(0xFF,
		(dwColor >> 16) & 0xFF,  // Red
		(dwColor >> 8) & 0xFF,   // Green
		dwColor & 0xFF);         // Blue

	// ����󻺳����Ȼ���
	DWORD dwClearFlags = D3DCLEAR_TARGET;
	if (m_bDeviceModeFlag & _DXG_DEVICEMODE_ZBUFFER)
	{
		dwClearFlags |= D3DCLEAR_ZBUFFER;
	}

	hr = m_pd3dDevice->Clear(0, NULL, dwClearFlags, d3dColor, 1.0f, 0);

	// DirectX 9.0 Ǩ�ƣ��豸��ʧ����
	if (hr == D3DERR_DEVICELOST || hr == D3DERR_DEVICENOTRESET)
	{
		// �����豸��ʧ
		hr = HandleDeviceLost();
		if (FAILED(hr))
		{
			return hr;
		}

		// �豸�ָ������³���Clear
		hr = m_pd3dDevice->Clear(0, NULL, dwClearFlags, d3dColor, 1.0f, 0);
	}

	return hr;
}


/******************************************************************************************************************

	������ : CWHDXGraphicWindow::marySurface()

	���� :
	�������� :

	Ŀ�� :
	��� : HRESULT

	[����][�޸���] : �޸�����

*******************************************************************************************************************/
// DirectX 9.0 Ǩ�ƣ���дCreatePrimarySurface����
HRESULT CWHDXGraphicWindow::CreatePrimarySurface()
{
	HRESULT hr = S_OK;


	if (m_bScreenModeFlag & _DXG_SCREENMODE_WINDOW)
	{
		RECT				rcWork;
		RECT				rcTemp;

		GetWindowRect(m_hWnd, &rcTemp);

		// ���ô��ڿͻ�����СΪ��Ϸ�ֱ���
		SetRect(&rcTemp, 0, 0, m_stDisplayInfo.wWidth, m_stDisplayInfo.wHeight);

		// ���ݴ�����ʽ�������ڴ�С
		AdjustWindowRectEx(&rcTemp, GetWindowStyle(m_hWnd), GetMenu(m_hWnd) != NULL, GetWindowExStyle(m_hWnd));

		// ʹ�ô������������С���ô��ڡ�������ʼ���ꡣ
		SetWindowPos(m_hWnd, NULL, 0, 0, rcTemp.right - rcTemp.left, rcTemp.bottom - rcTemp.top,
			SWP_NOMOVE | SWP_NOZORDER | SWP_NOACTIVATE);

		// ���ô���Ϊ���ö�
		SetWindowPos(m_hWnd, HWND_NOTOPMOST, 0, 0, 0, 0, SWP_NOSIZE | SWP_NOMOVE | SWP_NOACTIVATE);

		// ȷ�������ڹ�������
		SystemParametersInfo(SPI_GETWORKAREA, 0, &rcWork, 0);
		GetWindowRect(m_hWnd, &rcTemp);
		if (rcTemp.left < rcWork.left)
		{
			rcTemp.left = rcWork.left;
		}
		if (rcTemp.top < rcWork.top)
		{
			rcTemp.top = rcWork.top;
		}

		// ���ô��ڵ���ʼ���ꡣ
		SetWindowPos(m_hWnd, NULL, rcTemp.left, rcTemp.top, 0, 0, SWP_NOSIZE | SWP_NOZORDER | SWP_NOACTIVATE);
	}

	// DirectX 9.0 Ǩ�ƣ�����Ҫ�����ü�����ʹ���ӿڽ��вü�
	// �����ӿ�
	D3DVIEWPORT9 viewport;
	viewport.X = 0;
	viewport.Y = 0;
	viewport.Width = m_stDisplayInfo.wWidth;
	viewport.Height = m_stDisplayInfo.wHeight;
	viewport.MinZ = 0.0f;
	viewport.MaxZ = 1.0f;

	if (m_pd3dDevice)
	{
		hr = m_pd3dDevice->SetViewport(&viewport);
		if (FAILED(hr))
		{
			return hr;
		}
	}
	//////////////////////////////////////////////////////////////////////////////////////////////////////////////////

	return S_OK;
}


/******************************************************************************************************************

	������ : CWHDXGraphicWindow::UpdateBoundsWnd()

	���� :
	�������� :

	Ŀ�� :
	��� : VOID

	[����][�޸���] : �޸�����

*******************************************************************************************************************/
VOID CWHDXGraphicWindow::UpdateBoundsWnd()
{
	if (m_bScreenModeFlag & _DXG_SCREENMODE_WINDOW)
	{
		GetClientRect(m_hWnd, &m_rcWindow);
		ClientToScreen(m_hWnd, (POINT*)&m_rcWindow);
		ClientToScreen(m_hWnd, (POINT*)&m_rcWindow + 1);
	}
	else
	{
		SetRect(&m_rcWindow, 0, 0, GetSystemMetrics(SM_CXSCREEN), GetSystemMetrics(SM_CYSCREEN));
	}
}


/******************************************************************************************************************

	������ : CWHDXGraphicWindow::CreateZBuffer()

	���� :
	�������� :

	Ŀ�� :
	���� : GUID* pDeviceGUID
	��� : HRESULT

	[����][�޸���] : �޸�����

*******************************************************************************************************************/
// DirectX 9.0 Ǩ�ƣ���дCreateZBuffer����ΪCreateDepthStencilSurface
HRESULT CWHDXGraphicWindow::CreateZBuffer(const GUID* pDeviceGUID)
{
	HRESULT hr;

	if (!m_pd3dDevice)
		return E_FAIL;

	// DirectX 9.0 Ǩ�ƣ�����豸����
	D3DCAPS9 d3dCaps;
	hr = m_pd3dDevice->GetDeviceCaps(&d3dCaps);
	if (FAILED(hr))
		return hr;

	// DirectX 9.0 Ǩ�ƣ�����豸֧����Z�����HSR������Ҫ��Ȼ���
	if (d3dCaps.RasterCaps & D3DPRASTERCAPS_ZBUFFERLESSHSR)
		return S_OK;

	// DirectX 9.0 Ǩ�ƣ��������ģ�����
	// ͨ����Ȼ������豸����ʱ�Ѿ��Զ�������������Ҫ�ǻ�ȡ����
	hr = m_pd3dDevice->GetDepthStencilSurface(&m_pDepthStencilSurface);
	if (FAILED(hr))
	{
		// ���û���Զ��������ֶ�����һ��
		hr = m_pd3dDevice->CreateDepthStencilSurface(
			m_stDisplayInfo.wWidth,
			m_stDisplayInfo.wHeight,
			D3DFMT_D16,              // 16λ��ȸ�ʽ
			D3DMULTISAMPLE_NONE,     // �޶��ز���
			0,                       // ���ز�������
			TRUE,                    // �ɶ���
			&m_pDepthStencilSurface,
			NULL);

		if (FAILED(hr))
			return hr;

		// �������ģ�����
		hr = m_pd3dDevice->SetDepthStencilSurface(m_pDepthStencilSurface);
		if (FAILED(hr))
			return hr;
	}

	return S_OK;
}


// DirectX 9.0 Ǩ�ƣ�����Init3DDeviceObjects����
HRESULT CWHDXGraphicWindow::Init3DDeviceObjects()
{
	if (!m_pd3dDevice)
	{
		return E_FAIL;
	}

	// DirectX 9.0 Ǩ�ƣ����ñ任����
	D3DXMATRIX matWorld, matView, matProj;

	FLOAT fHalfWidth = (FLOAT)((FLOAT)m_stDisplayInfo.wWidth / 2.0f);
	FLOAT fHalfHeight = (FLOAT)((FLOAT)m_stDisplayInfo.wHeight / 2.0f);

	// Ϊ2D��Ϸ��������ͶӰ����
	D3DXVECTOR3 vEyePt(0, 0, -fHalfHeight);
	D3DXVECTOR3 vLookatPt(0, 0, 0);
	D3DXVECTOR3 vUpVec(0, 1, 0);

	D3DXMatrixIdentity(&matWorld);
	D3DXMatrixLookAtLH(&matView, &vEyePt, &vLookatPt, &vUpVec);
	D3DXMatrixPerspectiveFovLH(&matProj, D3DX_PI / 2, fHalfWidth / fHalfHeight, 0.1f, 1000.0f);

	// DirectX 9.0 Ǩ�ƣ����ò���
	D3DMATERIAL9 mtrl;
	ZeroMemory(&mtrl, sizeof(mtrl));
	mtrl.Diffuse.r = mtrl.Diffuse.g = mtrl.Diffuse.b = 0.1f;
	mtrl.Ambient.r = mtrl.Ambient.g = mtrl.Ambient.b = 1.0f;
	mtrl.Diffuse.a = mtrl.Ambient.a = 1.0f;
	m_pd3dDevice->SetMaterial(&mtrl);

	// DirectX 9.0 Ǩ�ƣ����ñ任����
	m_pd3dDevice->SetTransform(D3DTS_WORLD, &matWorld);
	m_pd3dDevice->SetTransform(D3DTS_VIEW, &matView);
	m_pd3dDevice->SetTransform(D3DTS_PROJECTION, &matProj);

	// DirectX 9.0 Ǩ�ƣ�������Ⱦ״̬
	m_pd3dDevice->SetRenderState(D3DRS_AMBIENT, 0xFFFFFFFF);
	m_pd3dDevice->SetRenderState(D3DRS_DITHERENABLE, FALSE);
	m_pd3dDevice->SetRenderState(D3DRS_CULLMODE, D3DCULL_NONE);
	m_pd3dDevice->SetRenderState(D3DRS_ZENABLE, D3DZB_FALSE);
	m_pd3dDevice->SetRenderState(D3DRS_FILLMODE, D3DFILL_SOLID);

	// DirectX 9.0 Ǩ�ƣ����������׶�״̬
	m_pd3dDevice->SetTextureStageState(0, D3DTSS_COLORARG1, D3DTA_TEXTURE);
	m_pd3dDevice->SetTextureStageState(0, D3DTSS_COLORARG2, D3DTA_DIFFUSE);
	m_pd3dDevice->SetTextureStageState(0, D3DTSS_COLOROP, D3DTOP_MODULATE);

	// DirectX 9.0 Ǩ�ƣ����ò�����״̬����������׶�״̬�еĹ��������ã�
	m_pd3dDevice->SetSamplerState(0, D3DSAMP_MINFILTER, D3DTEXF_POINT);
	m_pd3dDevice->SetSamplerState(0, D3DSAMP_MAGFILTER, D3DTEXF_POINT);
	m_pd3dDevice->SetSamplerState(0, D3DSAMP_MIPFILTER, D3DTEXF_NONE);

	return S_OK;
}


/******************************************************************************************************************

	������ : CWHDXGraphicWindow::CreateGameFont()

	���� :
	�������� :

	Ŀ�� :
	���� : LPCSTR szFontName
			 INT nXsize
			 INT nYsize
			 BYTE bFontType
	��� : VOID

	[����][�޸���] : �޸�����

*******************************************************************************************************************/
HFONT CWHDXGraphicWindow::CreateGameFont(LPCSTR szFontName, INT nHeight, INT nWidth, INT nWeight, BOOL bItalic, BOOL bULine, BOOL bStrikeOut, DWORD dwCharSet)
{
	INT nYsize;

	// DirectX 9.0 Ǩ�ƣ��Ӻ󻺳�����ȡ�豸������
	if (m_pddsOffscreenSurface)
	{
		HDC hDC;
		if (SUCCEEDED(m_pddsOffscreenSurface->GetDC(&hDC)))
		{
			nYsize = -MulDiv(nHeight, GetDeviceCaps(hDC, LOGPIXELSY), 72);
			m_pddsOffscreenSurface->ReleaseDC(hDC);
		}
		else
		{	// ����޷���ȡDC��ʹ��Ĭ��ֵ
			nYsize = -nHeight;
		}
	}
	else
	{
		// ���û�к󻺳���棬ʹ��Ĭ��ֵ
		nYsize = -nHeight;
	}

	if (szFontName)
	{
		return CreateFont(nYsize, nWidth, 0, 0, nWeight, bItalic, bULine, bStrikeOut, dwCharSet,
			OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS,
			DEFAULT_QUALITY, DEFAULT_PITCH, szFontName);
	}
	else
	{
		if (m_pszDefFont[0] != '\0')
		{
			return CreateFont(nYsize, nWidth, 0, 0, nWeight, bItalic, bULine, bStrikeOut, dwCharSet,
				OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS,
				DEFAULT_QUALITY, DEFAULT_PITCH, m_pszDefFont);
		}
		else
		{
			return CreateFont(nYsize, nWidth, 0, 0, nWeight, bItalic, bULine, bStrikeOut, dwCharSet,
				OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS,
				DEFAULT_QUALITY, DEFAULT_PITCH, "����");
		}
	}
}

VOID CWHDXGraphicWindow::SetDefFont(CHAR* pszFont, INT nFontSize)
{
	// ����Ѿ������˻������壬������Ա����´�����
	if (m_hDefGameFont != NULL)
	{
		DeleteObject(m_hDefGameFont);
		m_hDefGameFont = NULL;
	}

	if (pszFont == NULL)
	{
		if (m_pszDefFont[0] != '\0')
		{
			m_hDefGameFont = CreateGameFont(m_pszDefFont, 9);
		}
		else
		{
			m_hDefGameFont = CreateGameFont("����", 9);
			ZeroMemory(m_pszDefFont, MAX_PATH);
		}
	}
	else
	{
		m_hDefGameFont = CreateGameFont(pszFont, 9);
		strcpy(m_pszDefFont, pszFont);
	}
}



VOID CWHDXGraphicWindow::StringPlus(CHAR* szResult, CHAR* szSrc, ...)
{
	INT nCnt = 0;

	va_list vaMarker;
	CHAR* pszString;


	//	ZeroMemory(szResult, strlen(szResult));
	va_start(vaMarker, szSrc);
	pszString = szSrc;

	while (TRUE)
	{
		strcpy(&szResult[strlen(szResult)], pszString);
		if (!strcmp(pszString, ""))		break;
		pszString = (CHAR*)va_arg(vaMarker, CHAR*);
	}

	va_end(vaMarker);
}


BOOL CWHDXGraphicWindow::BelongStr(CHAR* pszSrc, CHAR* pszCmp)
{
	INT nCmpLen = strlen(pszCmp);
	INT nSrcLen = strlen(pszSrc);

	if (nCmpLen > nSrcLen)
	{
		return FALSE;
	}

	CHAR* pszCheck = NULL;
	while (pszCheck = strchr(pszSrc, pszCmp[0]))
	{
		if (!memcmp(pszCheck, pszCmp, nCmpLen))
		{
			return TRUE;
		}
		pszSrc = pszCheck + 1;
	}

	return FALSE;
}

CHAR* CWHDXGraphicWindow::IntToStr(INT nNum)
{
	static CHAR szResult[MAX_PATH];
	ZeroMemory(szResult, MAX_PATH);
	_itoa(nNum, szResult, 10);
	return szResult;
}




/******************************************************************************************************************

	������ : CWHDXGraphicWindow::PutsHan()

	���� :
	�������� :

	Ŀ�� :
	���� : LPDIRECTDRAWSURFACE7 pSurface
			 INT nX
			 INT nY
			 COLORREF foreColor
			 COLORREF backColor
			 CHAR* szFormat
			 ...
	��� : VOID

	[����][�޸���] : �޸�����

*******************************************************************************************************************/
// DirectX 9.0 Ǩ�ƣ�����PutsHan����ǩ��
VOID CWHDXGraphicWindow::PutsHan(LPDIRECT3DSURFACE9 pSurface, INT nX, INT nY, COLORREF foreColor, COLORREF backColor, CHAR* szText, HFONT hFont)
{
	HDC		hDC;
	HFONT	hOldFont;

	if (NULL == pSurface)
	{
		pSurface = m_pddsOffscreenSurface;
	}

	if (NULL == pSurface)
	{
		return;
	}

	if (NULL == hFont)
	{
		hFont = m_hDefGameFont;
	}

	HRESULT hr = pSurface->GetDC(&hDC);

	if (FAILED(hr))
	{
		return;
	}

	if (backColor == RGB(0, 0, 0))
		SetBkMode(hDC, TRANSPARENT);
	else
		SetBkColor(hDC, backColor);

	INT ntet = GetLastError();

	hOldFont = (HFONT)SelectObject(hDC, hFont);

	SetTextColor(hDC, foreColor);

	TextOut(hDC, nX, nY, szText, strlen(szText));

	SelectObject(hDC, hOldFont);

	if (hFont != m_hDefGameFont)
	{
		DeleteObject(hFont);
		hFont = NULL;
	}

	pSurface->ReleaseDC(hDC);
}

// DirectX 9.0 Ǩ�ƣ�����PutsHanA����ǩ��
VOID CWHDXGraphicWindow::PutsHanA(LPDIRECT3DSURFACE9 pSurface, INT nX, INT nY, COLORREF foreColor, COLORREF backColor, CHAR* szText, HFONT hFont)
{
	HDC		hDC;
	HFONT	hOldFont;

	if (NULL == pSurface)
	{
		pSurface = m_pddsOffscreenSurface;
	}

	if (NULL == pSurface)
	{
		return;
	}

	if (NULL == hFont)
	{
		hFont = m_hDefGameFont;
	}

	HRESULT hr = pSurface->GetDC(&hDC);

	if (FAILED(hr))
	{
		return;
	}

	if (backColor == RGB(0, 0, 0))		SetBkMode(hDC, TRANSPARENT);
	else									SetBkColor(hDC, backColor);

	hOldFont = (HFONT)SelectObject(hDC, hFont);

	SetTextColor(hDC, foreColor);

	TextOut(hDC, nX, nY, szText, strlen(szText));

	SelectObject(hDC, hOldFont);

	if (hFont != m_hDefGameFont)
	{
		DeleteObject(hFont);
		hFont = NULL;
	}

	pSurface->ReleaseDC(hDC);
}

/******************************************************************************************************************

	������ : CWHDXGraphicWindow::PutsHan()

	���� :
	�������� :

	Ŀ�� :
	���� : LPDIRECTDRAWSURFACE7 pSurface
			 INT nX
			 INT nY
			 COLORREF foreColor
			 COLORREF backColor
			 CHAR* szFormat
			 ...
	��� : VOID

	[����][�޸���] : �޸�����

*******************************************************************************************************************/
// DirectX 9.0 Ǩ�ƣ�����PutsHan����ǩ����RECT�汾��
VOID CWHDXGraphicWindow::PutsHan(LPDIRECT3DSURFACE9 pSurface, RECT rc, COLORREF foreColor, COLORREF backColor, CHAR* szText, HFONT hFont)
{
	HDC		hDC;
	HFONT	hOldFont;

	if (NULL == pSurface)
	{
		pSurface = m_pddsOffscreenSurface;
	}

	if (NULL == pSurface)
	{
		return;
	}

	if (NULL == hFont)
	{
		hFont = m_hDefGameFont;
	}

	HRESULT hr = pSurface->GetDC(&hDC);

	if (FAILED(hr))
	{
		return;
	}

	if (backColor == RGB(0, 0, 0))		SetBkMode(hDC, TRANSPARENT);
	else									SetBkColor(hDC, backColor);

	hOldFont = (HFONT)SelectObject(hDC, hFont);

	SetTextColor(hDC, foreColor);

	DrawText(hDC, szText, -1, &rc, DT_CENTER | DT_VCENTER | DT_SINGLELINE);

	SelectObject(hDC, hOldFont);

	if (hFont != m_hDefGameFont)
	{
		DeleteObject(hFont);
		hFont = NULL;
	}

	pSurface->ReleaseDC(hDC);
}


// DirectX 9.0 Ǩ�ƣ�����PutsHanLeft����ǩ��
VOID CWHDXGraphicWindow::PutsHanLeft(LPDIRECT3DSURFACE9 pSurface, RECT rc, COLORREF foreColor, COLORREF backColor, CHAR* szText, HFONT hFont)
{
	HDC		hDC;
	HFONT	hOldFont;

	if (NULL == pSurface)
	{
		pSurface = m_pddsOffscreenSurface;
	}

	if (NULL == pSurface)
	{
		return;
	}

	if (NULL == hFont)
	{
		hFont = m_hDefGameFont;
	}

	HRESULT hr = pSurface->GetDC(&hDC);

	if (FAILED(hr))
	{
		return;
	}

	if (backColor == RGB(0, 0, 0))		SetBkMode(hDC, TRANSPARENT);
	else									SetBkColor(hDC, backColor);

	hOldFont = (HFONT)SelectObject(hDC, hFont);

	SetTextColor(hDC, foreColor);

	DrawText(hDC, szText, -1, &rc, DT_LEFT | DT_VCENTER | DT_SINGLELINE);

	SelectObject(hDC, hOldFont);

	if (hFont != m_hDefGameFont)
	{
		DeleteObject(hFont);
		hFont = NULL;
	}

	pSurface->ReleaseDC(hDC);
}

// DirectX 9.0 Ǩ�ƣ�����PutsHanEx����ǩ��
VOID CWHDXGraphicWindow::PutsHanEx(LPDIRECT3DSURFACE9 pSurface, RECT rc, COLORREF foreColor, COLORREF backColor, CHAR* szText, HFONT hFont)
{
	HDC		hDC;
	HFONT	hOldFont;


	if (NULL == pSurface)
		pSurface = m_pddsOffscreenSurface;

	if (NULL == pSurface)
		return;

	if (NULL == hFont)
		hFont = m_hDefGameFont;

	HRESULT hr = pSurface->GetDC(&hDC);

	if (FAILED(hr))
	{
		return;
	}

	if (backColor == RGB(0, 0, 0))		SetBkMode(hDC, TRANSPARENT);
	else									SetBkColor(hDC, backColor);

	hOldFont = (HFONT)SelectObject(hDC, hFont);

	SetTextColor(hDC, foreColor);

	// ��������ռ�ݵ�������
	SIZE sizeTmp;
	GetTextExtentPoint32(hDC, szText, strlen(szText), &sizeTmp);

	// ����ʱ��
	if (sizeTmp.cx < rc.right - rc.left)
		DrawText(hDC, szText, strlen(szText), &rc, DT_CENTER | DT_VCENTER | DT_SINGLELINE);
	else
	{
		INT nHeight = DrawTextEx(hDC, szText, strlen(szText), &rc, DT_LEFT | DT_WORDBREAK | DT_CALCRECT, NULL);
		RECT rcTmp;
		rcTmp.top = rc.top + (rc.bottom - rc.top) / 2 - nHeight / 2;
		rcTmp.bottom = rcTmp.top + nHeight;
		rcTmp.left = rc.left;
		rcTmp.right = rc.right;
		DrawText(hDC, szText, strlen(szText), &rcTmp, DT_LEFT | DT_WORDBREAK);
	}

	SelectObject(hDC, hOldFont);

	if (hFont != m_hDefGameFont)
	{
		DeleteObject(hFont);
		hFont = NULL;
	}

	pSurface->ReleaseDC(hDC);
}


// DirectX 9.0 Ǩ�ƣ�����GetStrLength����ǩ��
SIZE CWHDXGraphicWindow::GetStrLength(LPDIRECT3DSURFACE9 pSurface, HFONT hFont, CHAR* szFormat)
{
	HDC		hDC;
	SIZE	sSize;

	sSize.cx = 0;
	sSize.cy = 0;

	HFONT	hOldFont;

	if (NULL == pSurface)
	{
		pSurface = m_pddsOffscreenSurface;
	}

	if (NULL == pSurface)
	{
		return sSize;
	}

	if (NULL == hFont)
	{
		hFont = m_hDefGameFont;
	}

	HRESULT hr = pSurface->GetDC(&hDC);

	if (FAILED(hr))
	{
		return sSize;
	}

	hOldFont = (HFONT)SelectObject(hDC, hFont);

	GetTextExtentPoint32(hDC, szFormat, strlen(szFormat), &sSize);

	SelectObject(hDC, hOldFont);

	if (hFont != m_hDefGameFont)
	{
		DeleteObject(hFont);
		hFont = NULL;
	}

	pSurface->ReleaseDC(hDC);

	return sSize;
}


/*BOOL CWHDXGraphicWindow::StringDivideLen(INT nDivideLen, INT& nDividedLine, CHAR* szSrc,CDLList<CHAR*>* m_pxpStr)
{
	CHAR szTmpCheck[MAX_PATH];
	CHAR szResult[MAX_PATH];

	nDividedLine = 1;

	INT nStartLen	= 0;
	INT nEndLen		= 0;
	INT nResultLen	= 0;
	INT nWordCheck	= 0;

	if ( szSrc[0] != NULL )
	{
		ZeroMemory(szResult,MAX_PATH);

		for ( INT nCnt = 0; nCnt < (INT)strlen(szSrc); nCnt++)
		{
			ZeroMemory(szTmpCheck, MAX_PATH);
			nEndLen = nCnt+1;

			// ��ȡ��ǰ���ֵ��ַ�����
			memcpy(&szTmpCheck, &szSrc[nStartLen], nEndLen-nStartLen);
			INT nsLen;
			nsLen = strlen(szTmpCheck);

			// ����ȡ�Ĳ��ֳ�����������ʱ��
			if (nsLen > nDivideLen )
			{
				// �����ǰ��˫�ֽ��ַ���
				if ( szSrc[nEndLen-1] < 0 )
				{
					// �����ǰǰ����ַ�����˫�ֽ��ַ���
					if ( !(nWordCheck%2) )
					{
						nStartLen += strlen(szTmpCheck)-1;
						nCnt--;
						CHAR* pszNewLine;
						pszNewLine = new CHAR[nResultLen+1];
						memcpy(pszNewLine,szResult,nResultLen);
						pszNewLine[nResultLen]=NULL;
						m_pxpStr->AddNode(pszNewLine);
						nResultLen = 0;
						nDividedLine++;
					}
					else
					{
						nStartLen += strlen(szTmpCheck)-2;
						nCnt -= 2;
						CHAR* pszNewLine;
						pszNewLine = new CHAR[nResultLen];
						memcpy(pszNewLine,szResult,nResultLen-1);
						pszNewLine[nResultLen-1]=NULL;
						m_pxpStr->AddNode(pszNewLine);
						nResultLen = 0;
						nDividedLine++;
						nWordCheck--;
					}
				}
				// ���ֽ��ַ���
				// ֻ�迼�ǵ�ǰǰһ�ֽڵĲ��֡�
				else
				{
					nStartLen += strlen(szTmpCheck)-1;
					nCnt--;
					CHAR* pszNewLine;
					pszNewLine = new CHAR[nResultLen+1];
					memcpy(pszNewLine,szResult,nResultLen);
					pszNewLine[nResultLen]=NULL;
					m_pxpStr->AddNode(pszNewLine);
					nResultLen=0;
					nDividedLine++;
				}
			}
			else if(szSrc[nEndLen-1]=='\n')	// ǿ�ƻ���    \n�ᵼ�´���????
			{
				nStartLen += strlen(szTmpCheck)-1;
				CHAR* pszNewLine;
				pszNewLine = new CHAR[nResultLen+1];
				memcpy(pszNewLine,szResult,nResultLen);
				pszNewLine[nResultLen]=NULL;
				m_pxpStr->AddNode(pszNewLine);
				nResultLen=0;
				nDividedLine++;
			}
			else
			{
				if ( szSrc[nEndLen-1] < 0 )
					nWordCheck++;

				szResult[nResultLen] = szSrc[nEndLen-1];
				nResultLen++;
			}
		}
		if(nResultLen!=0)
		{
			CHAR* pszNewLine;
			pszNewLine = new CHAR[nResultLen+1];
			memcpy(pszNewLine,szResult,nResultLen);
			pszNewLine[nResultLen]=NULL;
			m_pxpStr->AddNode(pszNewLine);
			nDividedLine++;
			nResultLen=0;
		}
		return TRUE;
	}
	return FALSE;
}
*/


BOOL CWHDXGraphicWindow::StringDivide(INT nDivideWidth, INT& nDividedLine, CHAR* szSrc, CHAR* szResult, HFONT hFont)
{
	CHAR szTmpCheck[MAX_PATH];

	INT nStartLen, nEndLen, nResultLen, nWordCheck;

	nStartLen = nEndLen = nResultLen = nWordCheck = 0;
	nDividedLine = 1;


	HDC		hDC;
	HFONT	hOldFont;

	// DirectX 9.0 Ǩ�ƣ����º󻺳��������
	if (NULL == hFont || m_pddsOffscreenSurface == NULL)
	{
		hFont = m_hDefGameFont;
	}

	m_pddsOffscreenSurface->GetDC(&hDC);
	//	if ( FAILED( m_pddsOffscreenSurface->GetDC(&hDC) ) )
	//	{
	//		return FALSE;
	//	}

	hOldFont = (HFONT)SelectObject(hDC, hFont);

	if (szSrc[0] != '\0')
	{
		for (INT nCnt = 0; nCnt < (INT)strlen(szSrc); nCnt++)
		{
			ZeroMemory(szTmpCheck, MAX_PATH);

			nEndLen = nCnt + 1;

			/*			// ����ַ���С��������ַ���С�������Ϊ���ֵ��
						if ( nEndLen >= MAX_PATH )
						{
							szResult[MAX_PATH-1] = NULL;
							break;
						}
			*/
			// ��ȡ��ǰ���ֵ��ַ�����
			memcpy(&szTmpCheck, &szSrc[nStartLen], nEndLen - nStartLen);

			SIZE sizeLen = { 0, 0 };

			GetTextExtentPoint32(hDC, szTmpCheck, strlen(szTmpCheck), &sizeLen);

			// ����ȡ�Ĳ��ֳ�����������ʱ��
			if (sizeLen.cx > nDivideWidth)
			{
				// ��ǰΪ˫�ֽ��ַ�ʱ��
				if (szSrc[nEndLen - 1] < 0)
				{
					// ��ǰǰ����ַ�����˫�ֽ��ַ�ʱ
					if (!(nWordCheck % 2))
					{
						nStartLen += strlen(szTmpCheck) - 1;
						nCnt--;
						szResult[nResultLen] = '`';
						nResultLen++;
						nDividedLine++;
					}
					else
					{
						nStartLen += strlen(szTmpCheck) - 2;
						nCnt -= 2;
						szResult[nResultLen - 1] = '`';
						nDividedLine++;
						nWordCheck--;
					}
				}
				// ���ֽ��ַ���
				// ֻ�迼�ǵ�ǰ�ֽ�ǰ��Ĳ��֡�
				else
				{
					nStartLen += strlen(szTmpCheck) - 1;
					nCnt--;
					szResult[nResultLen] = '`';
					nResultLen++;
					nDividedLine++;
				}
			}
			else
			{
				if (szSrc[nEndLen - 1] < 0)
				{
					nWordCheck++;


					szResult[nResultLen] = szSrc[nEndLen - 1];
					nResultLen++;

					nWordCheck++;

					nCnt++;
					nEndLen = nCnt + 1;
					szResult[nResultLen] = szSrc[nEndLen - 1];
					nResultLen++;
				}
				else
				{
					szResult[nResultLen] = szSrc[nEndLen - 1];
					nResultLen++;
				}
			}
		}
	}

	if (szSrc[0] == '\0')
	{
		*szResult = '\0';
	}

	SelectObject(hDC, hOldFont);

	if (hFont != m_hDefGameFont)
	{
		DeleteObject(hFont);
		hFont = NULL;
	}

	// DirectX 9.0 Ǩ�ƣ����º󻺳��������
	m_pddsOffscreenSurface->ReleaseDC(hDC);

	return TRUE;

}

// ���� ��������ظ�ʽ�����㲢���ض�Ӧ�� RGB ͨ��������Ϣ
DXG_MASKINFO CWHDXGraphicWindow::GetRGBMaskInfoIN16Bits(LPDIRECT3DSURFACE9 pSurface)
{
	DXG_MASKINFO stRGBMaskInfo;
	ZeroMemory(&stRGBMaskInfo, sizeof(DXG_MASKINFO));

	if (pSurface == NULL)
		pSurface = m_pddsOffscreenSurface;

	if (pSurface)
	{
		// DirectX 9.0 Ǩ�ƣ���ȡ��������
		HRESULT hr = pSurface->GetDesc(&m_stSurfDesc);
		if (SUCCEEDED(hr))
		{
			// DirectX 9.0 Ǩ�ƣ����ݸ�ʽ����RGB������Ϣ
			switch (m_stSurfDesc.Format)
			{
			case D3DFMT_R5G6B5:  // 16λ RGB 565��ʽ
				stRGBMaskInfo.dwRMask = 0xF800;  // ��ɫ���� (λ15-11)
				stRGBMaskInfo.dwGMask = 0x07E0;  // ��ɫ���� (λ10-5)
				stRGBMaskInfo.dwBMask = 0x001F;  // ��ɫ���� (λ4-0)
				stRGBMaskInfo.bRShift = 11;      // ��ɫλ��
				stRGBMaskInfo.bGShift = 5;       // ��ɫλ��
				stRGBMaskInfo.bBShift = 0;       // ��ɫλ��
				stRGBMaskInfo.bRCnt = 5;         // ��ɫλ��
				stRGBMaskInfo.bGCnt = 6;         // ��ɫλ��
				stRGBMaskInfo.bBCnt = 5;         // ��ɫλ��
				break;

			case D3DFMT_X1R5G5B5:  // 16λ RGB 555��ʽ
				stRGBMaskInfo.dwRMask = 0x7C00;  // ��ɫ���� (λ14-10)
				stRGBMaskInfo.dwGMask = 0x03E0;  // ��ɫ���� (λ9-5)
				stRGBMaskInfo.dwBMask = 0x001F;  // ��ɫ���� (λ4-0)
				stRGBMaskInfo.bRShift = 10;      // ��ɫλ��
				stRGBMaskInfo.bGShift = 5;       // ��ɫλ��
				stRGBMaskInfo.bBShift = 0;       // ��ɫλ��
				stRGBMaskInfo.bRCnt = 5;         // ��ɫλ��
				stRGBMaskInfo.bGCnt = 5;         // ��ɫλ��
				stRGBMaskInfo.bBCnt = 5;         // ��ɫλ��
				break;

			case D3DFMT_A1R5G5B5:
				stRGBMaskInfo.dwRMask = 0x7C00;  // ��ɫ���� (λ13-9��15λ�е�10-6λ)
				stRGBMaskInfo.dwGMask = 0x03E0;  // ��ɫ���� (λ8-4)
				stRGBMaskInfo.dwBMask = 0x001F;  // ��ɫ���� (λ3-0)
				stRGBMaskInfo.bRShift = 9;       // ��ɫ��ʼλ�ƣ���λ15��ʼ�㣬��9λ�����λ��
				stRGBMaskInfo.bGShift = 4;       // ��ɫ��ʼλ��
				stRGBMaskInfo.bBShift = 0;       // ��ɫ��ʼλ��
				stRGBMaskInfo.bRCnt = 5;         // ��ɫλ��
				stRGBMaskInfo.bGCnt = 5;         // ��ɫλ��
				stRGBMaskInfo.bBCnt = 5;         // ��ɫλ��
				break;

			default:	// D3DFMT_X8R8G8B8
				stRGBMaskInfo.dwRMask = 0x00FF0000;  // ��ɫ���� (λ23-16)
				stRGBMaskInfo.dwGMask = 0x0000FF00;  // ��ɫ���� (λ15-8)
				stRGBMaskInfo.dwBMask = 0x000000FF;  // ��ɫ���� (λ7-0)
				stRGBMaskInfo.bRShift = 16;          // ��ɫ��ʼλ�ƣ���16λ�����λ��
				stRGBMaskInfo.bGShift = 8;           // ��ɫ��ʼλ��
				stRGBMaskInfo.bBShift = 0;           // ��ɫ��ʼλ��
				stRGBMaskInfo.bRCnt = 8;             // ��ɫλ����8λ��0-255��
				stRGBMaskInfo.bGCnt = 8;             // ��ɫλ��
				stRGBMaskInfo.bBCnt = 8;             // ��ɫλ��
				break;
			}
		}
	}

	return stRGBMaskInfo;
}



////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// ��ͼ�������ϡ�

WORD CWHDXGraphicWindow::ConvertColor24To16(COLORREF dwColor)
{
	WORD wRet;

	INT nRed = RGB_GETRED(dwColor);
	INT nGreen = RGB_GETGREEN(dwColor);
	INT nBlue = RGB_GETBLUE(dwColor);

	nRed = (nRed * (1 << m_stBitsMaskInfo.bRCnt)) / (1 << 8);
	nGreen = (nGreen * (1 << m_stBitsMaskInfo.bGCnt)) / (1 << 8);
	nBlue = (nBlue * (1 << m_stBitsMaskInfo.bBCnt)) / (1 << 8);

	wRet = (nRed << m_stBitsMaskInfo.bRShift) | (nGreen << m_stBitsMaskInfo.bGShift) | (nBlue << m_stBitsMaskInfo.bBShift);
	return wRet;
}


/******************************************************************************************************************

	������ : CWHDXGraphicWindow::DrawWithGDI()

	���� :
	�������� :

	Ŀ�� :
	���� : RECT rc
			 LPDIRECTDRAWSURFACE7 pSurface
			 DWORD dwColor
			 DWORD dwPenStyleFlg
			 BYTE bKind
	��� : HRESULT

	[����][�޸���] : �޸�����

*******************************************************************************************************************/
// DirectX 9.0 Ǩ�ƣ�����DrawWithGDI����
HRESULT CWHDXGraphicWindow::DrawWithGDI(RECT* prc, LPDIRECT3DSURFACE9 pSurface, DWORD dwColor, BYTE bKind)
{
	POINT	pt;
	HDC		hDC;
	HPEN	hPen, hOldPen;
	HBRUSH	hBrush, hOldBrush;
	HRESULT hr;

	if (pSurface == NULL)
	{
		pSurface = m_pddsOffscreenSurface;
	}

	if (pSurface == NULL)
	{
		return E_FAIL;
	}

	if (FAILED(hr = pSurface->GetDC(&hDC)))
	{
		return E_FAIL;
	}

	switch (bKind)
	{
	case 0:
	{
		hPen = CreatePen(PS_SOLID, NULL, dwColor);

		hOldPen = (HPEN)SelectObject(hDC, hPen);

		MoveToEx(hDC, prc->left, prc->top, &pt);
		LineTo(hDC, prc->right, prc->bottom);

		SelectObject(hDC, hOldPen);
		DeleteObject(hPen);
		hPen = NULL;
	}
	break;
	case 1:
	{
		hBrush = CreateSolidBrush(dwColor);

		hOldBrush = (HBRUSH)SelectObject(hDC, hBrush);

		FrameRect(hDC, prc, hBrush);

		SelectObject(hDC, hOldBrush);
		DeleteObject(hBrush);
		hBrush = NULL;
	}
	break;
	case 2:
	{
		hBrush = CreateSolidBrush(dwColor);

		hOldBrush = (HBRUSH)SelectObject(hDC, hBrush);

		FillRect(hDC, prc, hBrush);

		SelectObject(hDC, hOldBrush);
		DeleteObject(hBrush);
		hBrush = NULL;
	}
	break;
	case 3:
	{
		hBrush = CreateSolidBrush(dwColor);

		hOldBrush = (HBRUSH)SelectObject(hDC, hBrush);

		Ellipse(hDC, prc->left, prc->top, prc->right, prc->bottom);

		SelectObject(hDC, hOldBrush);
		DeleteObject(hBrush);
		hBrush = NULL;
	}
	break;
	case 4:
	{
		hPen = CreatePen(PS_SOLID, NULL, dwColor);

		hOldPen = (HPEN)SelectObject(hDC, hPen);

		Ellipse(hDC, prc->left, prc->top, prc->right, prc->bottom);

		SelectObject(hDC, hOldPen);
		DeleteObject(hPen);
		hPen = NULL;
	}
	break;
	default:
		break;
	}

	pSurface->ReleaseDC(hDC);

	return S_OK;
}


/******************************************************************************************************************

	������ : CWHDXGraphicWindow::DrawSprite()

	���� :
	�������� :

	Ŀ�� : ������ơ������ػ��ơ���Ļ����ü���Դ����ü���
	���� : INT nX
			 INT nY
			 INT nXSize
			 INT nYSize
			 WORD* pwSrc
	��� : VOID

	[����][�޸���] : �޸�����

*******************************************************************************************************************/
// DirectX 9.0 Ǩ�ƣ�����DrawWithImage����
BOOL CWHDXGraphicWindow::DrawWithImage(INT nX, INT nY, INT nXSize, INT nYSize, WORD* pwSrc)
{
	RECT			rc;
	D3DLOCKED_RECT lockedRect;

	INT	nWidth = nXSize;
	INT	nHeight = nYSize;
	INT	nXOffset = 0;
	INT	nYOffset = 0;
	INT	nStartX = 0;
	INT	nStartY = 0;
	INT	nEndX = m_stDisplayInfo.wWidth - 1;
	INT	nEndY = m_stDisplayInfo.wHeight - 1;

	if (m_pddsOffscreenSurface != NULL)
	{
		if (nX < nStartX)
		{
			nXOffset = nStartX - nX;
			nWidth = nXSize - nXOffset;
		}
		if ((nX + nXSize - 1) > nEndX)
			nWidth = nEndX - nX - nXOffset + 1;
		if (nY < nStartY)
		{
			nYOffset = nStartY - nY;
			nHeight = nYSize - nYOffset;
		}
		if ((nY + nYSize - 1) > nEndY)
			nHeight = nEndY - nY - nYOffset + 1;

		if ((nWidth > 0) && (nHeight > 0))
		{
			rc.left = nXOffset;
			rc.right = nXOffset + nWidth;
			rc.top = nYOffset;
			rc.bottom = nYOffset + nHeight;

			// DirectX 9.0 Ǩ�ƣ�ʹ��Direct3D9��������
			if (FAILED(m_pddsOffscreenSurface->LockRect(&lockedRect, NULL, 0)))
			{
				return FALSE;
			}

			WORD* pwdDst = (WORD*)lockedRect.pBits;
			LONG lWidth = lockedRect.Pitch >> 1;

			for (INT y = rc.top; y < rc.bottom; y++)
			{
				for (INT x = rc.left; x < rc.right; x++)
				{
					if (pwSrc[y * nXSize + x] != 0)
					{
						pwdDst[((y + nY) * lWidth) + (nX + x)] = pwSrc[y * nXSize + x];
					}
				}
			}

			m_pddsOffscreenSurface->UnlockRect();
		}
		return TRUE;
	}
	return FALSE;
}


BOOL CWHDXGraphicWindow::DrawWithImagePerLineClipRgn(INT nX, INT nY, INT nXSize, INT nYSize, WORD* pwSrc, WORD wClipWidth, WORD wClipHeight)
{
	RECT			rc;
	// DirectX 9.0 Ǩ�ƣ�DDSURFACEDESC2���滻ΪD3DLOCKED_RECT
	// DDSURFACEDESC2	ddsd;

	INT	nWidth = nXSize;
	INT	nHeight = nYSize;
	INT	nXOffset = 0;
	INT	nYOffset = 0;
	INT	nStartX = 0;
	INT	nStartY = 0;
	INT	nEndX = wClipWidth - 1;
	INT	nEndY = wClipHeight - 1;

	if (m_pddsOffscreenSurface != NULL)
	{
		if (nX < nStartX)
		{
			nXOffset = nStartX - nX;
			nWidth = nXSize - nXOffset;
		}
		if ((nX + nXSize - 1) > nEndX)
			nWidth = nEndX - nX - nXOffset + 1;
		if (nY < nStartY)
		{
			nYOffset = nStartY - nY;
			nHeight = nYSize - nYOffset;
		}
		if ((nY + nYSize - 1) > nEndY)
			nHeight = nEndY - nY - nYOffset + 1;

		if ((nWidth > 0) && (nHeight > 0))
		{
			rc.left = nXOffset;
			rc.right = nXOffset + nWidth;
			rc.top = nYOffset;
			rc.bottom = nYOffset + nHeight;

			D3DLOCKED_RECT lockedRect;
			LONG	lWidth;

			if (FAILED(m_pddsOffscreenSurface->LockRect(&lockedRect, NULL, 0)))
			{
				return FALSE;
			}

			WORD* pwdDst = (WORD*)lockedRect.pBits;
			lWidth = lockedRect.Pitch >> 1;

			for (INT y = rc.top; y < rc.bottom; y++)
			{
				memcpy(&pwdDst[((y + nY) * lWidth) + (nX + rc.left)], &pwSrc[y * nXSize + rc.left], (rc.right - rc.left) * 2);
			}

			m_pddsOffscreenSurface->UnlockRect();
		}
		return TRUE;
	}
	return FALSE;
}



/******************************************************************************************************************

	������ : CWHDXGraphicWindow::DrawWithSurface()

	���� :
	�������� :

	Ŀ�� :
	���� : LPDIRECTDRAWSURFACE7 pddsSrc
			 RECT* prcSrc
			 RECT* prcDst
	��� : BOOL

	[����][�޸���] : �޸�����

*******************************************************************************************************************/
// DirectX 9.0 Ǩ�ƣ�����DrawWithSurface����
BOOL CWHDXGraphicWindow::DrawWithSurface(INT nX, INT nY, INT nXSize, INT nYSize, LPDIRECT3DSURFACE9 pddsSrc)
{
	RECT	rc;
	RECT	rcDst;
	INT		nXOffset = 0;
	INT		nYOffset = 0;
	INT		nWidth = nXSize;
	INT		nHeight = nYSize;
	INT		nStartX = 0;
	INT		nStartY = 0;

	INT		nEndX = m_stDisplayInfo.wWidth - 1;
	INT		nEndY = m_stDisplayInfo.wHeight - 1;

	if (m_pddsOffscreenSurface != NULL && pddsSrc != NULL)
	{
		if (nX < nStartX)
		{
			nXOffset = nStartX - nX;
			nWidth = nXSize - nXOffset;
		}
		if ((nX + nXSize - 1) > nEndX)
			nWidth = nEndX - nX - nXOffset + 1;
		if (nY < nStartY)
		{
			nYOffset = nStartY - nY;
			nHeight = nYSize - nYOffset;
		}
		if ((nY + nYSize - 1) > nEndY)
			nHeight = nEndY - nY - nYOffset + 1;

		if ((nWidth > 0) && (nHeight > 0))
		{
			rc.left = nXOffset;
			rc.right = nXOffset + nWidth;
			rc.top = nYOffset;
			rc.bottom = nYOffset + nHeight;

			rcDst.left = rc.left + nX;
			rcDst.top = rc.top + nY;
			rcDst.right = rc.right + nX;
			rcDst.bottom = rc.bottom + nY;

			// DirectX 9.0 Ǩ�ƣ�ʹ��StretchRect���Blt����
			HRESULT hr = m_pd3dDevice->StretchRect(pddsSrc, &rc, m_pddsOffscreenSurface, &rcDst, D3DTEXF_NONE);
			if (FAILED(hr))
			{
				return FALSE;
			}
		}
		return TRUE;
	}
	return FALSE;
}


BOOL CWHDXGraphicWindow::DrawWithImageForCompMemToMem(INT nX, INT nY, INT nXSize, INT nYSize, WORD* pwSrc, INT nDstXSize, INT nDstYSize, WORD* pwDst, WORD wChooseColor1, WORD wChooseColor2)
{
	RECT			rc;

	INT	nWidth = nXSize;
	INT	nHeight = nYSize;
	INT	nXOffset = 0;
	INT	nYOffset = 0;
	INT	nStartX = 0;
	INT	nStartY = 0;
	INT	nEndX = nDstXSize - 1;
	INT	nEndY = nDstYSize - 1;

	if (pwDst != NULL)
	{
		if (nX < nStartX)
		{
			nXOffset = nStartX - nX;
			nWidth = nXSize - nXOffset;
		}
		if ((nX + nXSize - 1) > nEndX)
			nWidth = nEndX - nX - nXOffset + 1;
		if (nY < nStartY) {
			nYOffset = nStartY - nY;
			nHeight = nYSize - nYOffset;
		}
		if ((nY + nYSize - 1) > nEndY)
			nHeight = nEndY - nY - nYOffset + 1;

		if ((nWidth > 0) && (nHeight > 0))
		{
			rc.left = nXOffset;
			rc.right = nXOffset + nWidth;
			rc.top = nYOffset;
			rc.bottom = nYOffset + nHeight;

			INT nWidthStart = 0;
			INT nWidthEnd = 0;
			INT nCurrWidth = 0;
			INT nCntCopyWord = 0;
			INT nYCnt = 0;
			INT nLastWidth = 0;

			FLOAT rBlueRate, rGreenRate, bRedRate;
			BYTE  bRedSrc, bGreenSrc, bBlueSrc;
			BYTE  bRedWantedColor, bGreenWantedColor, bBlueWantedColor;
			WORD  wPixel;

			// Y��ü���
			for (nYCnt = 0; nYCnt < rc.top; nYCnt++)
			{
				nWidthEnd += pwSrc[nWidthStart];
				nWidthStart++;
				nWidthEnd++;
				nWidthStart = nWidthEnd;
			}

			// ȷ��Y����ʵ��ѭ����Count��
			for (nYCnt = rc.top; nYCnt < rc.bottom; nYCnt++)
			{
				// ��ȡһ�еĳ��ȣ�����Ϊ��λ����
				nWidthEnd += pwSrc[nWidthStart];
				nWidthStart++;

				// ���еĳ��Ȼ��Ƶ���Ļ�ϡ�
				for (INT x = nWidthStart; x < nWidthEnd; )
				{
					if (pwSrc[x] == 0xC0)
					{
						x++;
						nCntCopyWord = pwSrc[x];
						x++;
						nCurrWidth += nCntCopyWord;
					}
					else if (pwSrc[x] == 0xC1)
					{
						x++;
						nCntCopyWord = pwSrc[x];
						x++;

						nLastWidth = nCurrWidth;
						nCurrWidth += nCntCopyWord;

						if (rc.left > nCurrWidth || rc.right < nLastWidth)
						{
							x += nCntCopyWord;
						}
						else
						{
							// ��rc.left��Ϊ��׼���ü����򱻽�ȡ�������
							if (nLastWidth < rc.left && rc.left <= nCurrWidth)
							{
								if (((nYCnt + nY) * nDstXSize) + (rc.left + nX) < 0)
									return FALSE;

								x += (rc.left - nLastWidth);
								memcpy(&pwDst[((nYCnt + nY) * nDstXSize) + (rc.left + nX)], &pwSrc[x], sizeof(WORD) * (nCurrWidth - rc.left));
								x += (nCurrWidth - rc.left);
							}
							// ��rc.right��Ϊ��׼���ü����򱻽�ȡ�������
							else if (nLastWidth <= rc.right && rc.right < nCurrWidth)
							{
								memcpy(&pwDst[((nYCnt + nY) * nDstXSize) + (nLastWidth + nX)], &pwSrc[x], sizeof(WORD) * (rc.right - nLastWidth));
								x += nCntCopyWord;
							}
							else
							{
								memcpy(&pwDst[((nYCnt + nY) * nDstXSize) + (nLastWidth + nX)], &pwSrc[x], sizeof(WORD) * nCntCopyWord);
								x += nCntCopyWord;
							}
						}
					}
					else if (pwSrc[x] == 0xC2 || pwSrc[x] == 0xC3)
					{
						WORD wDyingKind, wChooseColor;
						wDyingKind = pwSrc[x];
						switch (wDyingKind)
						{
						case 0xC2:
							wChooseColor = wChooseColor1;
							break;
						case 0xC3:
							wChooseColor = wChooseColor2;
							break;
						}
						x++;
						nCntCopyWord = pwSrc[x];
						x++;

						nLastWidth = nCurrWidth;
						nCurrWidth += nCntCopyWord;

						if (rc.left > nCurrWidth || rc.right < nLastWidth)
						{
							x += nCntCopyWord;
						}
						else
						{
							// ��rc.left��Ϊ��׼���ü����򱻽�ȡ�������
							if (nLastWidth < rc.left && rc.left <= nCurrWidth)
							{
								x += (rc.left - nLastWidth);
								for (INT nCheck = 0; nCheck < nCurrWidth - rc.left; nCheck++)
								{
									wPixel = wChooseColor;
									bBlueWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									wPixel = pwSrc[x + nCheck];
									bBlueSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									rBlueRate = (FLOAT)((FLOAT)bBlueSrc / (FLOAT)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));
									rGreenRate = (FLOAT)((FLOAT)bGreenSrc / (FLOAT)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
									bRedRate = (FLOAT)((FLOAT)bRedSrc / (FLOAT)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));

									bBlueWantedColor = (BYTE)(((FLOAT)bBlueWantedColor * rBlueRate));
									bGreenWantedColor = (BYTE)(((FLOAT)bGreenWantedColor * rGreenRate));
									bRedWantedColor = (BYTE)(((FLOAT)bRedWantedColor * bRedRate));

									if (bBlueWantedColor > (m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift))
										bBlueWantedColor = (BYTE)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift);
									if (bGreenWantedColor > (m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift))
										bGreenWantedColor = (BYTE)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift);
									if (bRedWantedColor > (m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift))
										bRedWantedColor = (BYTE)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift);

									if (((nYCnt + nY) * nDstXSize) + (nLastWidth + nX + nCheck) < 0)
										return FALSE;

									pwDst[((nYCnt + nY) * nDstXSize) + (nLastWidth + nX + nCheck)] = ((bRedWantedColor << m_stBitsMaskInfo.bRShift) |
										(bGreenWantedColor << m_stBitsMaskInfo.bGShift) |
										(bBlueWantedColor << m_stBitsMaskInfo.bBShift));
								}
								x += (nCurrWidth - rc.left);
							}
							// ��rc.right��Ϊ��׼���ü����򱻽�ȡ�������
							else if (nLastWidth <= rc.right && rc.right < nCurrWidth)
							{
								for (INT nCheck = 0; nCheck < rc.right - nLastWidth; nCheck++)
								{
									wPixel = wChooseColor;
									bBlueWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									wPixel = pwSrc[x + nCheck];
									bBlueSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									rBlueRate = (FLOAT)((FLOAT)bBlueSrc / (FLOAT)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));
									rGreenRate = (FLOAT)((FLOAT)bGreenSrc / (FLOAT)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
									bRedRate = (FLOAT)((FLOAT)bRedSrc / (FLOAT)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));

									bBlueWantedColor = (BYTE)(((FLOAT)bBlueWantedColor * rBlueRate));
									bGreenWantedColor = (BYTE)(((FLOAT)bGreenWantedColor * rGreenRate));
									bRedWantedColor = (BYTE)(((FLOAT)bRedWantedColor * bRedRate));

									if (bBlueWantedColor > (m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift))
										bBlueWantedColor = (BYTE)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift);
									if (bGreenWantedColor > (m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift))
										bGreenWantedColor = (BYTE)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift);
									if (bRedWantedColor > (m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift))
										bRedWantedColor = (BYTE)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift);

									pwDst[((nYCnt + nY) * nDstXSize) + (nLastWidth + nX + nCheck)] = ((bRedWantedColor << m_stBitsMaskInfo.bRShift) |
										(bGreenWantedColor << m_stBitsMaskInfo.bGShift) |
										(bBlueWantedColor << m_stBitsMaskInfo.bBShift));
								}
								x += nCntCopyWord;
							}
							else
							{
								for (INT nCheck = 0; nCheck < nCntCopyWord; nCheck++)
								{
									wPixel = wChooseColor;
									bBlueWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									wPixel = pwSrc[x + nCheck];
									bBlueSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									rBlueRate = (FLOAT)((FLOAT)bBlueSrc / (FLOAT)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));
									rGreenRate = (FLOAT)((FLOAT)bGreenSrc / (FLOAT)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
									bRedRate = (FLOAT)((FLOAT)bRedSrc / (FLOAT)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));

									bBlueWantedColor = (BYTE)(((FLOAT)bBlueWantedColor * rBlueRate));
									bGreenWantedColor = (BYTE)(((FLOAT)bGreenWantedColor * rGreenRate));
									bRedWantedColor = (BYTE)(((FLOAT)bRedWantedColor * bRedRate));

									if (bBlueWantedColor > (m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift))
										bBlueWantedColor = (BYTE)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift);
									if (bGreenWantedColor > (m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift))
										bGreenWantedColor = (BYTE)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift);
									if (bRedWantedColor > (m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift))
										bRedWantedColor = (BYTE)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift);

									pwDst[((nYCnt + nY) * nDstXSize) + (nLastWidth + nX + nCheck)] = ((bRedWantedColor << m_stBitsMaskInfo.bRShift) |
										(bGreenWantedColor << m_stBitsMaskInfo.bGShift) |
										(bBlueWantedColor << m_stBitsMaskInfo.bBShift));
								}
								x += nCntCopyWord;
							}
						}
					}
					else
					{
						return FALSE;
					}
				}
				// ����β�ƶ�����һ�еĿ�ͷ��
				nWidthEnd++;

				nWidthStart = nWidthEnd;
				nCurrWidth = 0;
			}
		}

		return TRUE;
	}
	return FALSE;
}


BOOL CWHDXGraphicWindow::DrawWithImageForComp(INT nX, INT nY, RECT* prcSrc, WORD* pwSrc, WORD wChooseColor1, WORD wChooseColor2, LPDIRECT3DTEXTURE9 pddsDst)
{
	LPDIRECT3DSURFACE9 ddsDst = NULL;
	RECT			rc;

	if (!prcSrc || !pwSrc)
	{
		return FALSE;
	}

	if (pddsDst)
	{
		if (FAILED(pddsDst->GetSurfaceLevel(0, &ddsDst)))
		{
			return FALSE;
		}
	}
	else
	{
		ddsDst = m_pddsOffscreenSurface;
	}

	INT	nWidth = prcSrc->right - prcSrc->left;
	INT	nHeight = prcSrc->bottom - prcSrc->top;
	INT	nXOffset = prcSrc->left;
	INT	nYOffset = prcSrc->top;
	INT	nStartX = 0;
	INT	nStartY = 0;
	INT	nEndX = m_stDisplayInfo.wWidth - 1;
	INT	nEndY = m_stDisplayInfo.wHeight - 1;

	if (ddsDst != NULL)
	{
		if (nX < nStartX)
		{
			if (nXOffset < -nX)
			{
				nXOffset = -nX;
			}
			nWidth = prcSrc->right - nXOffset;
		}
		if ((nX + prcSrc->right - prcSrc->left - 1) > nEndX)
		{
			nWidth = nEndX - nX - nXOffset + 1;
		}
		if (nY < nStartY)
		{
			if (nYOffset < -nY)
			{
				nYOffset = -nY;
			}
			nHeight = prcSrc->bottom - nYOffset;
		}
		if ((nY + prcSrc->bottom - prcSrc->top - 1) > nEndY)
		{
			nHeight = nEndY - nY - nYOffset + 1;
		}

		if ((nWidth > 0) && (nHeight > 0))
		{
			rc.left = nXOffset;
			rc.right = nXOffset + nWidth;
			rc.top = nYOffset;
			rc.bottom = nYOffset + nHeight;

			D3DLOCKED_RECT lockedRect;
			LONG	lWidth;

			if (FAILED(ddsDst->LockRect(&lockedRect, NULL, 0)))
			{
				return FALSE;
			}

			WORD* pwdDst = (WORD*)lockedRect.pBits;
			lWidth = lockedRect.Pitch >> 1;

			INT nWidthStart = 0;
			INT nWidthEnd = 0;
			INT nCurrWidth = 0;
			INT nCntCopyWord = 0;
			INT nYCnt = 0;
			INT nLastWidth = 0;

			FLOAT rBlueRate, rGreenRate, bRedRate;
			BYTE  bRedSrc, bGreenSrc, bBlueSrc;
			BYTE  bRedWantedColor, bGreenWantedColor, bBlueWantedColor;
			WORD  wPixel;

			// Y��ü���
			for (nYCnt = 0; nYCnt < rc.top; nYCnt++)
			{
				nWidthEnd += pwSrc[nWidthStart];
				nWidthStart++;
				nWidthEnd++;
				nWidthStart = nWidthEnd;
			}

			// ȷ��Y����ʵ��ѭ����Count��
			for (nYCnt = rc.top; nYCnt < rc.bottom; nYCnt++)
			{
				// ��ȡһ�еĳ��ȣ�����Ϊ��λ����
				nWidthEnd += pwSrc[nWidthStart];
				nWidthStart++;

				// ���еĳ��Ȼ��Ƶ���Ļ�ϡ�
				for (INT x = nWidthStart; x < nWidthEnd; )
				{
					if (pwSrc[x] == 0xC0)
					{
						x++;
						nCntCopyWord = pwSrc[x];
						x++;
						nCurrWidth += nCntCopyWord;
					}
					else if (pwSrc[x] == 0xC1)
					{
						x++;
						nCntCopyWord = pwSrc[x];
						x++;

						nLastWidth = nCurrWidth;
						nCurrWidth += nCntCopyWord;

						if (rc.left > nCurrWidth || rc.right < nLastWidth)
						{
							x += nCntCopyWord;
						}
						else
						{
							// ��rc.left��Ϊ��׼���ü����򱻽�ȡ�������
							if (nLastWidth < rc.left && rc.left <= nCurrWidth)
							{
								if (((nYCnt + nY) * lWidth) + (rc.left + nX) < 0)
									return FALSE;

								x += (rc.left - nLastWidth);
								memcpy(&pwdDst[((nYCnt + nY) * lWidth) + (rc.left + nX)], &pwSrc[x], sizeof(WORD) * (nCurrWidth - rc.left));
								x += (nCurrWidth - rc.left);
							}
							// ��rc.right��Ϊ��׼���ü����򱻽�ȡ�������
							else if (nLastWidth <= rc.right && rc.right < nCurrWidth)
							{
								memcpy(&pwdDst[((nYCnt + nY) * lWidth) + (nLastWidth + nX)], &pwSrc[x], sizeof(WORD) * (rc.right - nLastWidth));
								x += nCntCopyWord;
							}
							else
							{
								memcpy(&pwdDst[((nYCnt + nY) * lWidth) + (nLastWidth + nX)], &pwSrc[x], sizeof(WORD) * nCntCopyWord);
								x += nCntCopyWord;
							}
						}
					}
					else if (pwSrc[x] == 0xC2 || pwSrc[x] == 0xC3)
					{
						WORD wDyingKind, wChooseColor;
						wDyingKind = pwSrc[x];
						switch (wDyingKind)
						{
						case 0xC2:
							wChooseColor = wChooseColor1;
							break;
						case 0xC3:
							wChooseColor = wChooseColor2;
							break;
						}
						x++;
						nCntCopyWord = pwSrc[x];
						x++;

						nLastWidth = nCurrWidth;
						nCurrWidth += nCntCopyWord;

						if (rc.left > nCurrWidth || rc.right < nLastWidth)
						{
							x += nCntCopyWord;
						}
						else
						{
							// ��rc.left��Ϊ��׼���ü����򱻽�ȡ�������
							if (nLastWidth < rc.left && rc.left <= nCurrWidth)
							{
								x += (rc.left - nLastWidth);
								for (INT nCheck = 0; nCheck < nCurrWidth - rc.left; nCheck++)
								{
									wPixel = wChooseColor;
									bBlueWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									wPixel = pwSrc[x + nCheck];
									bBlueSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									rBlueRate = (FLOAT)((FLOAT)bBlueSrc / (FLOAT)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));
									rGreenRate = (FLOAT)((FLOAT)bGreenSrc / (FLOAT)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
									bRedRate = (FLOAT)((FLOAT)bRedSrc / (FLOAT)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));

									bBlueWantedColor = (BYTE)(((FLOAT)bBlueWantedColor * rBlueRate));
									bGreenWantedColor = (BYTE)(((FLOAT)bGreenWantedColor * rGreenRate));
									bRedWantedColor = (BYTE)(((FLOAT)bRedWantedColor * bRedRate));

									if (bBlueWantedColor > (m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift))
										bBlueWantedColor = (BYTE)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift);
									if (bGreenWantedColor > (m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift))
										bGreenWantedColor = (BYTE)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift);
									if (bRedWantedColor > (m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift))
										bRedWantedColor = (BYTE)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift);

									if (((nYCnt + nY) * lWidth) + (nLastWidth + nX + nCheck) < 0)
										return FALSE;

									pwdDst[((nYCnt + nY) * lWidth) + (nLastWidth + nX + nCheck)] = ((bRedWantedColor << m_stBitsMaskInfo.bRShift) |
										(bGreenWantedColor << m_stBitsMaskInfo.bGShift) |
										(bBlueWantedColor << m_stBitsMaskInfo.bBShift));
								}
								x += (nCurrWidth - rc.left);
							}
							// ��rc.right��Ϊ��׼���ü����򱻽�ȡ�������
							else if (nLastWidth <= rc.right && rc.right < nCurrWidth)
							{
								for (INT nCheck = 0; nCheck < rc.right - nLastWidth; nCheck++)
								{
									wPixel = wChooseColor;
									bBlueWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									wPixel = pwSrc[x + nCheck];
									bBlueSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									rBlueRate = (FLOAT)((FLOAT)bBlueSrc / (FLOAT)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));
									rGreenRate = (FLOAT)((FLOAT)bGreenSrc / (FLOAT)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
									bRedRate = (FLOAT)((FLOAT)bRedSrc / (FLOAT)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));

									bBlueWantedColor = (BYTE)(((FLOAT)bBlueWantedColor * rBlueRate));
									bGreenWantedColor = (BYTE)(((FLOAT)bGreenWantedColor * rGreenRate));
									bRedWantedColor = (BYTE)(((FLOAT)bRedWantedColor * bRedRate));

									if (bBlueWantedColor > (m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift))
										bBlueWantedColor = (BYTE)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift);
									if (bGreenWantedColor > (m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift))
										bGreenWantedColor = (BYTE)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift);
									if (bRedWantedColor > (m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift))
										bRedWantedColor = (BYTE)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift);

									pwdDst[((nYCnt + nY) * lWidth) + (nLastWidth + nX + nCheck)] = ((bRedWantedColor << m_stBitsMaskInfo.bRShift) |
										(bGreenWantedColor << m_stBitsMaskInfo.bGShift) |
										(bBlueWantedColor << m_stBitsMaskInfo.bBShift));
								}
								x += nCntCopyWord;
							}
							else
							{
								for (INT nCheck = 0; nCheck < nCntCopyWord; nCheck++)
								{
									wPixel = wChooseColor;
									bBlueWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									wPixel = pwSrc[x + nCheck];
									bBlueSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									rBlueRate = (FLOAT)((FLOAT)bBlueSrc / (FLOAT)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));
									rGreenRate = (FLOAT)((FLOAT)bGreenSrc / (FLOAT)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
									bRedRate = (FLOAT)((FLOAT)bRedSrc / (FLOAT)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));

									bBlueWantedColor = (BYTE)(((FLOAT)bBlueWantedColor * rBlueRate));
									bGreenWantedColor = (BYTE)(((FLOAT)bGreenWantedColor * rGreenRate));
									bRedWantedColor = (BYTE)(((FLOAT)bRedWantedColor * bRedRate));

									if (bBlueWantedColor > (m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift))
										bBlueWantedColor = (BYTE)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift);
									if (bGreenWantedColor > (m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift))
										bGreenWantedColor = (BYTE)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift);
									if (bRedWantedColor > (m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift))
										bRedWantedColor = (BYTE)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift);

									pwdDst[((nYCnt + nY) * lWidth) + (nLastWidth + nX + nCheck)] = ((bRedWantedColor << m_stBitsMaskInfo.bRShift) |
										(bGreenWantedColor << m_stBitsMaskInfo.bGShift) |
										(bBlueWantedColor << m_stBitsMaskInfo.bBShift));
								}
								x += nCntCopyWord;
							}
						}
					}
					else
					{
						// DirectX 9.0 Ǩ�ƣ�ʹ��UnlockRect���Unlock
						ddsDst->UnlockRect();
						return FALSE;
					}
				}
				// ����β�ƶ�����һ�еĿ�ͷ��
				nWidthEnd++;

				nWidthStart = nWidthEnd;
				nCurrWidth = 0;
			}
		}

		// DirectX 9.0 Ǩ�ƣ�ʹ��UnlockRect���Unlock
		ddsDst->UnlockRect();
		return TRUE;
	}
	return FALSE;
}


BOOL CWHDXGraphicWindow::DrawWithImageForComp(
	INT nX, INT nY, INT nXSize, INT nYSize, WORD* pwSrc, WORD wChooseColor1, WORD wChooseColor2)
{
	RECT			rc;
	int				imgW;
	int				imgH;

	if (IS_OUTSIDE(nX, nY, nXSize, nYSize))
		return TRUE;

	CREATE_CLIP_REGION(nX, nY, nXSize, nYSize);

	D3DLOCKED_RECT lockedRect;
	LONG	lWidth;

	if (FAILED(m_pddsOffscreenSurface->LockRect(&lockedRect, NULL, 0)))
	{
		// DirectX 9.0 Ǩ�ƣ�����������Ⱦ
		EndRender();

		return FALSE;
	}

	lWidth = lockedRect.Pitch >> 1;

	WORD* dst = (WORD*)lockedRect.pBits + (nY * lWidth) + nX;
	WORD* src = (WORD*)pwSrc;
	WORD* nxt, * tmp;

	int   y, pixelCnt, pixelPos, curPos;
	WORD  cc;         // choosecolor	
	BYTE  Rc, Gc, Bc; // choosecolor's RGB
	BYTE  Rs, Gs, Bs; // sourcecolor's RGB
	float Rr, Gr, Br; // sourcecolor's RGB Rate

	// Y����ü�
	for (y = 0; y < rc.top; y++)
		src += *src + 1;

	for (y = nY; y < nY + imgH; y++)
	{
		nxt = src + *src++ + 1;
		curPos = 0;

		while (src < nxt)
		{
			if (*src == 0xC0)
			{
				++src;
				curPos += *src++;
			}
			else if (*src == 0xC1)
			{
				++src;
				pixelCnt = *src++;

				if (curPos + pixelCnt > rc.left && curPos < rc.right)
				{
					// ���ü�
					if (curPos < rc.left)
					{
						src += (rc.left - curPos);
						pixelCnt -= (rc.left - curPos);
						curPos = rc.left;
					}

					// �Ҳ�ü�
					if (curPos + pixelCnt > rc.right)
						memcpy(dst + curPos - rc.left, src, (rc.right - curPos) << 1);
					else
						memcpy(dst + curPos - rc.left, src, pixelCnt << 1);
				}

				src += pixelCnt;
				curPos += pixelCnt;
			}
			else if (*src == 0xC2 || *src == 0xC3)
			{
				cc = *src == 0xC2 ? wChooseColor1 : wChooseColor2;

				// Ⱦɫ RGB
				Rc = (BYTE)((cc & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);
				Gc = (BYTE)((cc & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
				Bc = (BYTE)((cc & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);

				++src;
				pixelCnt = *src++;


				if (curPos + pixelCnt > rc.left && curPos < rc.right)
				{
					// ���ü�
					if (curPos < rc.left)
					{
						src += (rc.left - curPos);
						pixelCnt -= (rc.left - curPos);
						curPos = rc.left;
					}

					tmp = dst + curPos - rc.left;

					for (pixelPos = 0; pixelPos < pixelCnt; pixelPos++)
					{
						// �Ҳ�ü�
						if (curPos + pixelPos > rc.right)
							break;

						cc = src[pixelPos];

						// ԭʼ RGB
						Rs = (BYTE)((cc & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);
						Gs = (BYTE)((cc & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
						Bs = (BYTE)((cc & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);

						// ԭʼ RGB ����
						Rr = (float)((float)Rs / (float)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));
						Gr = (float)((float)Gs / (float)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
						Br = (float)((float)Bs / (float)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));

						// ����ȾɫRGB
						Rs = (BYTE)((float)Rc * Rr);
						Gs = (BYTE)((float)Gc * Gr);
						Bs = (BYTE)((float)Bc * Br);

						*tmp++ = Rs << m_stBitsMaskInfo.bRShift |
							Gs << m_stBitsMaskInfo.bGShift |
							Bs << m_stBitsMaskInfo.bBShift;
					}
				}

				src += pixelCnt;
				curPos += pixelCnt;
			}
			else
			{
				// DirectX 9.0 Ǩ�ƣ�ʹ��UnlockRect���Unlock
				m_pddsOffscreenSurface->UnlockRect();
				return FALSE;
			}
		}

		dst += lWidth;
	}

	// DirectX 9.0 Ǩ�ƣ�ʹ��UnlockRect���Unlock
	m_pddsOffscreenSurface->UnlockRect();

	return TRUE;
}



BOOL CWHDXGraphicWindow::DrawWithImageForCompClipRgnBase(INT nX, INT nY, INT nXSize, INT nYSize, WORD* pwSrc, WORD wClipWidth, WORD wClipHeight, WORD wChooseColor1, WORD wChooseColor2)
{
	RECT			rc;

	INT	nWidth = nXSize;
	INT	nHeight = nYSize;
	INT	nXOffset = 0;
	INT	nYOffset = 0;
	INT	nStartX = 0;
	INT	nStartY = 0;
	INT	nEndX = wClipWidth - 1;
	INT	nEndY = wClipHeight - 1;

	if (m_pddsOffscreenSurface != NULL)
	{
		if (nX < nStartX)
		{
			nXOffset = nStartX - nX;
			nWidth = nXSize - nXOffset;
		}
		if ((nX + nXSize - 1) > nEndX)
			nWidth = nEndX - nX - nXOffset + 1;
		if (nY < nStartY)
		{
			nYOffset = nStartY - nY;
			nHeight = nYSize - nYOffset;
		}
		if ((nY + nYSize - 1) > nEndY)
			nHeight = nEndY - nY - nYOffset + 1;

		if ((nWidth > 0) && (nHeight > 0))
		{
			rc.left = nXOffset;
			rc.right = nXOffset + nWidth;
			rc.top = nYOffset;
			rc.bottom = nYOffset + nHeight;

			D3DLOCKED_RECT lockedRect;
			LONG	lWidth;

			if (FAILED(m_pddsOffscreenSurface->LockRect(&lockedRect, NULL, 0)))
			{
				return FALSE;
			}

			WORD* pwdDst = (WORD*)lockedRect.pBits;
			lWidth = lockedRect.Pitch >> 1;

			INT nWidthStart = 0;
			INT nWidthEnd = 0;
			INT nCurrWidth = 0;
			INT nCntCopyWord = 0;
			INT nYCnt = 0;
			INT nLastWidth = 0;

			FLOAT rBlueRate, rGreenRate, bRedRate;
			BYTE  bRedSrc, bGreenSrc, bBlueSrc;
			BYTE  bRedWantedColor, bGreenWantedColor, bBlueWantedColor;
			WORD  wPixel;

			// Y��ü���
			for (nYCnt = 0; nYCnt < rc.top; nYCnt++)
			{
				nWidthEnd += pwSrc[nWidthStart];
				nWidthStart++;
				nWidthEnd++;
				nWidthStart = nWidthEnd;
			}

			// ȷ��Y����ʵ��ѭ����Count��
			for (nYCnt = rc.top; nYCnt < rc.bottom; nYCnt++)
			{
				// ��ȡһ�еĳ��ȣ�����Ϊ��λ����
				nWidthEnd += pwSrc[nWidthStart];
				nWidthStart++;

				// ���еĳ��Ȼ��Ƶ���Ļ�ϡ�
				for (INT x = nWidthStart; x < nWidthEnd; )
				{
					if (pwSrc[x] == 0xC0)
					{
						x++;
						nCntCopyWord = pwSrc[x];
						x++;
						nCurrWidth += nCntCopyWord;
					}
					else if (pwSrc[x] == 0xC1)
					{
						x++;
						nCntCopyWord = pwSrc[x];
						x++;

						nLastWidth = nCurrWidth;
						nCurrWidth += nCntCopyWord;

						if (rc.left > nCurrWidth || rc.right < nLastWidth)
						{
							x += nCntCopyWord;
						}
						else
						{
							// ��rc.left��Ϊ��׼���ü����򱻽�ȡ�������
							if (nLastWidth < rc.left && rc.left <= nCurrWidth)
							{
								if (((nYCnt + nY) * lWidth) + (rc.left + nX) < 0)
									return FALSE;

								x += (rc.left - nLastWidth);
								memcpy(&pwdDst[((nYCnt + nY) * lWidth) + (rc.left + nX)], &pwSrc[x], sizeof(WORD) * (nCurrWidth - rc.left));
								x += (nCurrWidth - rc.left);
							}
							// ��rc.right��Ϊ��׼���ü����򱻽�ȡ�������
							else if (nLastWidth <= rc.right && rc.right < nCurrWidth)
							{
								memcpy(&pwdDst[((nYCnt + nY) * lWidth) + (nLastWidth + nX)], &pwSrc[x], sizeof(WORD) * (rc.right - nLastWidth));
								x += nCntCopyWord;
							}
							else
							{
								memcpy(&pwdDst[((nYCnt + nY) * lWidth) + (nLastWidth + nX)], &pwSrc[x], sizeof(WORD) * nCntCopyWord);
								x += nCntCopyWord;
							}
						}
					}
					else if (pwSrc[x] == 0xC2 || pwSrc[x] == 0xC3)
					{
						WORD wDyingKind, wChooseColor;
						wDyingKind = pwSrc[x];
						switch (wDyingKind)
						{
						case 0xC2:
							wChooseColor = wChooseColor1;
							break;
						case 0xC3:
							wChooseColor = wChooseColor2;
							break;
						}
						x++;
						nCntCopyWord = pwSrc[x];
						x++;

						nLastWidth = nCurrWidth;
						nCurrWidth += nCntCopyWord;

						if (rc.left > nCurrWidth || rc.right < nLastWidth)
						{
							x += nCntCopyWord;
						}
						else
						{
							// ��rc.left��Ϊ��׼���ü����򱻽�ȡ�������
							if (nLastWidth < rc.left && rc.left <= nCurrWidth)
							{
								x += (rc.left - nLastWidth);
								for (INT nCheck = 0; nCheck < nCurrWidth - rc.left; nCheck++)
								{
									wPixel = wChooseColor;
									bBlueWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									wPixel = pwSrc[x + nCheck];
									bBlueSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									rBlueRate = (FLOAT)((FLOAT)bBlueSrc / (FLOAT)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));
									rGreenRate = (FLOAT)((FLOAT)bGreenSrc / (FLOAT)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
									bRedRate = (FLOAT)((FLOAT)bRedSrc / (FLOAT)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));

									bBlueWantedColor = (BYTE)(((FLOAT)bBlueWantedColor * rBlueRate));
									bGreenWantedColor = (BYTE)(((FLOAT)bGreenWantedColor * rGreenRate));
									bRedWantedColor = (BYTE)(((FLOAT)bRedWantedColor * bRedRate));

									if (bBlueWantedColor > (m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift))
										bBlueWantedColor = (BYTE)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift);
									if (bGreenWantedColor > (m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift))
										bGreenWantedColor = (BYTE)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift);
									if (bRedWantedColor > (m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift))
										bRedWantedColor = (BYTE)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift);

									if (((nYCnt + nY) * lWidth) + (nLastWidth + nX + nCheck) < 0)
										return FALSE;

									pwdDst[((nYCnt + nY) * lWidth) + (nLastWidth + nX + nCheck)] = ((bRedWantedColor << m_stBitsMaskInfo.bRShift) |
										(bGreenWantedColor << m_stBitsMaskInfo.bGShift) |
										(bBlueWantedColor << m_stBitsMaskInfo.bBShift));
								}
								x += (nCurrWidth - rc.left);
							}
							// ��rc.right��Ϊ��׼���ü����򱻽�ȡ�������
							else if (nLastWidth <= rc.right && rc.right < nCurrWidth)
							{
								for (INT nCheck = 0; nCheck < rc.right - nLastWidth; nCheck++)
								{
									wPixel = wChooseColor;
									bBlueWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									wPixel = pwSrc[x + nCheck];
									bBlueSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									rBlueRate = (FLOAT)((FLOAT)bBlueSrc / (FLOAT)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));
									rGreenRate = (FLOAT)((FLOAT)bGreenSrc / (FLOAT)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
									bRedRate = (FLOAT)((FLOAT)bRedSrc / (FLOAT)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));

									bBlueWantedColor = (BYTE)(((FLOAT)bBlueWantedColor * rBlueRate));
									bGreenWantedColor = (BYTE)(((FLOAT)bGreenWantedColor * rGreenRate));
									bRedWantedColor = (BYTE)(((FLOAT)bRedWantedColor * bRedRate));

									if (bBlueWantedColor > (m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift))
										bBlueWantedColor = (BYTE)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift);
									if (bGreenWantedColor > (m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift))
										bGreenWantedColor = (BYTE)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift);
									if (bRedWantedColor > (m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift))
										bRedWantedColor = (BYTE)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift);

									pwdDst[((nYCnt + nY) * lWidth) + (nLastWidth + nX + nCheck)] = ((bRedWantedColor << m_stBitsMaskInfo.bRShift) |
										(bGreenWantedColor << m_stBitsMaskInfo.bGShift) |
										(bBlueWantedColor << m_stBitsMaskInfo.bBShift));
								}
								x += nCntCopyWord;
							}
							else
							{
								for (INT nCheck = 0; nCheck < nCntCopyWord; nCheck++)
								{
									wPixel = wChooseColor;
									bBlueWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									wPixel = pwSrc[x + nCheck];
									bBlueSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									rBlueRate = (FLOAT)((FLOAT)bBlueSrc / (FLOAT)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));
									rGreenRate = (FLOAT)((FLOAT)bGreenSrc / (FLOAT)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
									bRedRate = (FLOAT)((FLOAT)bRedSrc / (FLOAT)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));

									bBlueWantedColor = (BYTE)(((FLOAT)bBlueWantedColor * rBlueRate));
									bGreenWantedColor = (BYTE)(((FLOAT)bGreenWantedColor * rGreenRate));
									bRedWantedColor = (BYTE)(((FLOAT)bRedWantedColor * bRedRate));

									if (bBlueWantedColor > (m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift))
										bBlueWantedColor = (BYTE)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift);
									if (bGreenWantedColor > (m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift))
										bGreenWantedColor = (BYTE)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift);
									if (bRedWantedColor > (m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift))
										bRedWantedColor = (BYTE)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift);

									pwdDst[((nYCnt + nY) * lWidth) + (nLastWidth + nX + nCheck)] = ((bRedWantedColor << m_stBitsMaskInfo.bRShift) |
										(bGreenWantedColor << m_stBitsMaskInfo.bGShift) |
										(bBlueWantedColor << m_stBitsMaskInfo.bBShift));
								}
								x += nCntCopyWord;
							}
						}
					}
					else
					{
						// DirectX 9.0 Ǩ�ƣ�ʹ��UnlockRect���Unlock
						m_pddsOffscreenSurface->UnlockRect();
						return FALSE;
					}
				}
				// ����β�ƶ�����һ�еĿ�ͷ��
				nWidthEnd++;

				nWidthStart = nWidthEnd;
				nCurrWidth = 0;
			}
		}

		// DirectX 9.0 Ǩ�ƣ�ʹ��UnlockRect���Unlock
		m_pddsOffscreenSurface->UnlockRect();
		return TRUE;
	}
	return FALSE;
}

BOOL CWHDXGraphicWindow::DrawWithImageForCompClipRgnBaseNoColor(INT nX, INT nY, INT nXSize, INT nYSize, WORD* pwSrc, WORD wClipWidth, WORD wClipHeight)
{
	RECT			rc;

	INT	nWidth = nXSize;
	INT	nHeight = nYSize;
	INT	nXOffset = 0;
	INT	nYOffset = 0;
	INT	nStartX = 0;
	INT	nStartY = 0;
	INT	nEndX = wClipWidth - 1;
	INT	nEndY = wClipHeight - 1;

	if (m_pddsOffscreenSurface != NULL)
	{
		if (nX < nStartX)
		{
			nXOffset = nStartX - nX;
			nWidth = nXSize - nXOffset;
		}
		if ((nX + nXSize - 1) > nEndX)
			nWidth = nEndX - nX - nXOffset + 1;
		if (nY < nStartY)
		{
			nYOffset = nStartY - nY;
			nHeight = nYSize - nYOffset;
		}
		if ((nY + nYSize - 1) > nEndY)
			nHeight = nEndY - nY - nYOffset + 1;

		if ((nWidth > 0) && (nHeight > 0))
		{
			rc.left = nXOffset;
			rc.right = nXOffset + nWidth;
			rc.top = nYOffset;
			rc.bottom = nYOffset + nHeight;

			D3DLOCKED_RECT lockedRect;
			LONG	lWidth;

			if (FAILED(m_pddsOffscreenSurface->LockRect(&lockedRect, NULL, 0)))
			{
				return FALSE;
			}

			WORD* pwdDst = (WORD*)lockedRect.pBits;
			lWidth = lockedRect.Pitch >> 1;

			INT nWidthStart = 0;
			INT nWidthEnd = 0;
			INT nCurrWidth = 0;
			INT nCntCopyWord = 0;
			INT nYCnt = 0;
			INT nLastWidth = 0;

			// Y��ü���
			for (nYCnt = 0; nYCnt < rc.top; nYCnt++)
			{
				nWidthEnd += pwSrc[nWidthStart];
				nWidthStart++;
				nWidthEnd++;
				nWidthStart = nWidthEnd;
			}

			// ȷ��Y����ʵ��ѭ����Count��
			for (nYCnt = rc.top; nYCnt < rc.bottom; nYCnt++)
			{
				// ��ȡһ�еĳ��ȣ�����Ϊ��λ����
				nWidthEnd += pwSrc[nWidthStart];
				nWidthStart++;

				// ���еĳ��Ȼ��Ƶ���Ļ�ϡ�
				for (INT x = nWidthStart; x < nWidthEnd; )
				{
					if (pwSrc[x] == 0xC0)
					{
						x++;
						nCntCopyWord = pwSrc[x];
						x++;
						nCurrWidth += nCntCopyWord;
					}
					else
					{
						x++;
						nCntCopyWord = pwSrc[x];
						x++;

						nLastWidth = nCurrWidth;
						nCurrWidth += nCntCopyWord;

						if (rc.left > nCurrWidth || rc.right < nLastWidth)
						{
							x += nCntCopyWord;
						}
						else
						{
							// ��rc.left��Ϊ��׼���ü����򱻽�ȡ�������
							if (nLastWidth < rc.left && rc.left <= nCurrWidth)
							{
								if (((nYCnt + nY) * lWidth) + (rc.left + nX) < 0)
									return FALSE;

								x += (rc.left - nLastWidth);
								memcpy(&pwdDst[((nYCnt + nY) * lWidth) + (rc.left + nX)], &pwSrc[x], sizeof(WORD) * (nCurrWidth - rc.left));
								x += (nCurrWidth - rc.left);
							}
							// ��rc.right��Ϊ��׼���ü����򱻽�ȡ�������
							else if (nLastWidth <= rc.right && rc.right < nCurrWidth)
							{
								memcpy(&pwdDst[((nYCnt + nY) * lWidth) + (nLastWidth + nX)], &pwSrc[x], sizeof(WORD) * (rc.right - nLastWidth));
								x += nCntCopyWord;
							}
							else
							{
								memcpy(&pwdDst[((nYCnt + nY) * lWidth) + (nLastWidth + nX)], &pwSrc[x], sizeof(WORD) * nCntCopyWord);
								x += nCntCopyWord;
							}
						}
					}
				}


				// ����β�ƶ�����һ�еĿ�ͷ��
				nWidthEnd++;

				nWidthStart = nWidthEnd;
				nCurrWidth = 0;
			}
		}

		// DirectX 9.0 Ǩ�ƣ�ʹ��UnlockRect���Unlock
		m_pddsOffscreenSurface->UnlockRect();
		return TRUE;
	}
	return FALSE;
}


BOOL CWHDXGraphicWindow::DrawWithABlendCompDataWithBackBuffer(INT nX, INT nY,
	INT nXSize, INT nYSize, WORD* pwSrc,
	WORD wClipWidth, WORD wClipHeight,
	WORD wChooseColor1, WORD wChooseColor2, BYTE bOpa)
{
	RECT			rc;

	INT	nWidth = nXSize;
	INT	nHeight = nYSize;
	INT	nXOffset = 0;
	INT	nYOffset = 0;
	INT	nStartX = 0;
	INT	nStartY = 0;
	INT	nEndX = wClipWidth - 1;
	INT	nEndY = wClipHeight - 1;

	WORD wDyingKind, wChooseColor;

	if (m_pddsOffscreenSurface != NULL)
	{
		if (nX < nStartX)
		{
			nXOffset = nStartX - nX;
			nWidth = nXSize - nXOffset;
		}
		if ((nX + nXSize - 1) > nEndX)
			nWidth = nEndX - nX - nXOffset + 1;
		if (nY < nStartY)
		{
			nYOffset = nStartY - nY;
			nHeight = nYSize - nYOffset;
		}
		if ((nY + nYSize - 1) > nEndY)
			nHeight = nEndY - nY - nYOffset + 1;

		if ((nWidth > 0) && (nHeight > 0))
		{
			rc.left = nXOffset;
			rc.right = nXOffset + nWidth;
			rc.top = nYOffset;
			rc.bottom = nYOffset + nHeight;

			D3DLOCKED_RECT lockedRect;
			LONG	lWidth;

			if (FAILED(m_pddsOffscreenSurface->LockRect(&lockedRect, NULL, 0)))
			{
				return FALSE;
			}

			WORD* pwdDst = (WORD*)lockedRect.pBits;
			lWidth = lockedRect.Pitch >> 1;

			INT nWidthStart = 0;
			INT nWidthEnd = 0;
			INT nCurrWidth = 0;
			INT nCntCopyWord = 0;
			INT nYCnt = 0;
			INT nLastWidth = 0;

			BYTE bRedDst, bGreenDst, bBlueDst;
			BYTE bRedSrc, bGreenSrc, bBlueSrc;
			BYTE bRedWantedColor, bGreenWantedColor, bBlueWantedColor;
			WORD wPixel;
			FLOAT rBlueRate, rGreenRate, bRedRate;

			// Y��ü���
			for (nYCnt = 0; nYCnt < rc.top; nYCnt++)
			{
				nWidthEnd += pwSrc[nWidthStart];
				nWidthStart++;
				nWidthEnd++;
				nWidthStart = nWidthEnd;
			}

			// ȷ��Y����ʵ��ѭ����Count��
			for (nYCnt = rc.top; nYCnt < rc.bottom; nYCnt++)
			{
				// ��ȡһ�еĳ��ȣ�����Ϊ��λ����
				nWidthEnd += pwSrc[nWidthStart];
				nWidthStart++;

				// �����еĳ�������Ļ����ʾ��
				for (INT x = nWidthStart; x < nWidthEnd; )
				{
					if (pwSrc[x] == 0xC0)
					{
						x++;
						nCntCopyWord = pwSrc[x];
						x++;
						nCurrWidth += nCntCopyWord;
					}
					else if (pwSrc[x] == 0xC1)
					{
						x++;
						nCntCopyWord = pwSrc[x];
						x++;

						nLastWidth = nCurrWidth;
						nCurrWidth += nCntCopyWord;

						if (rc.left > nCurrWidth || rc.right < nLastWidth)
						{
							x += nCntCopyWord;
						}
						else
						{
							// �� rc.left ��Ϊ��׼���������򱻽�ȡ�������
							if (nLastWidth < rc.left && rc.left <= nCurrWidth)
							{
								x += (rc.left - nLastWidth);
								for (INT nCheck = 0; nCheck < nCurrWidth - rc.left; nCheck++)
								{
									wPixel = pwSrc[x + nCheck];
									bBlueSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									wPixel = pwdDst[((nYCnt + nY) * lWidth) + (rc.left + nX + nCheck)];
									bBlueDst = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenDst = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedDst = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									bBlueDst = (BYTE)((bOpa * (bBlueDst - bBlueSrc) + 100 * bBlueSrc) / 100);
									bGreenDst = (BYTE)((bOpa * (bGreenDst - bGreenSrc) + 100 * bGreenSrc) / 100);
									bRedDst = (BYTE)((bOpa * (bRedDst - bRedSrc) + 100 * bRedSrc) / 100);

									if (((nYCnt + nY) * lWidth) + (rc.left + nX + nCheck) < 0)
										return FALSE;

									pwdDst[((nYCnt + nY) * lWidth) + (rc.left + nX + nCheck)] = ((bRedDst << m_stBitsMaskInfo.bRShift) |
										(bGreenDst << m_stBitsMaskInfo.bGShift) |
										(bBlueDst << m_stBitsMaskInfo.bBShift));
								}
								x += (nCurrWidth - rc.left);
							}
							// �� rc.right ��Ϊ��׼���������򱻽�ȡ�������
							else if (nLastWidth <= rc.right && rc.right < nCurrWidth)
							{
								for (INT nCheck = 0; nCheck < rc.right - nLastWidth; nCheck++)
								{
									wPixel = pwSrc[x + nCheck];
									bBlueSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									wPixel = pwdDst[((nYCnt + nY) * lWidth) + (nLastWidth + nX + nCheck)];
									bBlueDst = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenDst = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedDst = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									bBlueDst = (BYTE)((bOpa * (bBlueDst - bBlueSrc) + 100 * bBlueSrc) / 100);
									bGreenDst = (BYTE)((bOpa * (bGreenDst - bGreenSrc) + 100 * bGreenSrc) / 100);
									bRedDst = (BYTE)((bOpa * (bRedDst - bRedSrc) + 100 * bRedSrc) / 100);

									pwdDst[((nYCnt + nY) * lWidth) + (nLastWidth + nX + nCheck)] = ((bRedDst << m_stBitsMaskInfo.bRShift) |
										(bGreenDst << m_stBitsMaskInfo.bGShift) |
										(bBlueDst << m_stBitsMaskInfo.bBShift));
								}
								x += nCntCopyWord;
							}
							else
							{
								for (INT nCheck = 0; nCheck < nCntCopyWord; nCheck++)
								{
									wPixel = pwSrc[x + nCheck];
									bBlueSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									wPixel = pwdDst[((nYCnt + nY) * lWidth) + (nLastWidth + nX + nCheck)];
									bBlueDst = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenDst = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedDst = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									bBlueDst = (BYTE)((bOpa * (bBlueDst - bBlueSrc) + 100 * bBlueSrc) / 100);
									bGreenDst = (BYTE)((bOpa * (bGreenDst - bGreenSrc) + 100 * bGreenSrc) / 100);
									bRedDst = (BYTE)((bOpa * (bRedDst - bRedSrc) + 100 * bRedSrc) / 100);

									pwdDst[((nYCnt + nY) * lWidth) + (nLastWidth + nX + nCheck)] = ((bRedDst << m_stBitsMaskInfo.bRShift) |
										(bGreenDst << m_stBitsMaskInfo.bGShift) |
										(bBlueDst << m_stBitsMaskInfo.bBShift));
								}
								x += nCntCopyWord;
							}
						}
					}
					else if (pwSrc[x] == 0xC2 || pwSrc[x] == 0xC3)
					{
						wDyingKind = pwSrc[x];
						switch (wDyingKind)
						{
						case 0xC2:
							wChooseColor = wChooseColor1;
							break;
						case 0xC3:
							wChooseColor = wChooseColor2;
							break;
						}
						x++;
						nCntCopyWord = pwSrc[x];
						x++;

						nLastWidth = nCurrWidth;
						nCurrWidth += nCntCopyWord;

						if (rc.left > nCurrWidth || rc.right < nLastWidth)
						{
							x += nCntCopyWord;
						}
						else
						{
							// �� rc.left ��Ϊ��׼���������򱻽�ȡ�������
							if (nLastWidth < rc.left && rc.left <= nCurrWidth)
							{
								x += (rc.left - nLastWidth);
								for (INT nCheck = 0; nCheck < nCurrWidth - rc.left; nCheck++)
								{
									wPixel = wChooseColor;
									bBlueWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									wPixel = pwSrc[x + nCheck];
									bBlueSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									rBlueRate = (FLOAT)((FLOAT)bBlueSrc / (FLOAT)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));
									rGreenRate = (FLOAT)((FLOAT)bGreenSrc / (FLOAT)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
									bRedRate = (FLOAT)((FLOAT)bRedSrc / (FLOAT)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));

									bBlueWantedColor = (BYTE)(((FLOAT)bBlueWantedColor * rBlueRate));
									bGreenWantedColor = (BYTE)(((FLOAT)bGreenWantedColor * rGreenRate));
									bRedWantedColor = (BYTE)(((FLOAT)bRedWantedColor * bRedRate));

									if (bBlueWantedColor > (m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift))
										bBlueWantedColor = (BYTE)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift);
									if (bGreenWantedColor > (m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift))
										bGreenWantedColor = (BYTE)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift);
									if (bRedWantedColor > (m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift))
										bRedWantedColor = (BYTE)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift);


									wPixel = pwdDst[((nYCnt + nY) * lWidth) + (rc.left + nX + nCheck)];
									bBlueDst = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenDst = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedDst = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									bBlueDst = (BYTE)((bOpa * (bBlueDst - bBlueWantedColor) + 100 * bBlueWantedColor) / 100);
									bGreenDst = (BYTE)((bOpa * (bGreenDst - bGreenWantedColor) + 100 * bGreenWantedColor) / 100);
									bRedDst = (BYTE)((bOpa * (bRedDst - bRedWantedColor) + 100 * bRedWantedColor) / 100);

									if (((nYCnt + nY) * lWidth) + (rc.left + nX + nCheck) < 0)
										return FALSE;

									pwdDst[((nYCnt + nY) * lWidth) + (rc.left + nX + nCheck)] = ((bRedDst << m_stBitsMaskInfo.bRShift) |
										(bGreenDst << m_stBitsMaskInfo.bGShift) |
										(bBlueDst << m_stBitsMaskInfo.bBShift));
								}
								x += (nCurrWidth - rc.left);
							}
							// �� rc.right ��Ϊ��׼���������򱻽�ȡ�������
							else if (nLastWidth <= rc.right && rc.right < nCurrWidth)
							{
								for (INT nCheck = 0; nCheck < rc.right - nLastWidth; nCheck++)
								{
									wPixel = wChooseColor;
									bBlueWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									wPixel = pwSrc[x + nCheck];
									bBlueSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									rBlueRate = (FLOAT)((FLOAT)bBlueSrc / (FLOAT)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));
									rGreenRate = (FLOAT)((FLOAT)bGreenSrc / (FLOAT)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
									bRedRate = (FLOAT)((FLOAT)bRedSrc / (FLOAT)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));

									bBlueWantedColor = (BYTE)(((FLOAT)bBlueWantedColor * rBlueRate));
									bGreenWantedColor = (BYTE)(((FLOAT)bGreenWantedColor * rGreenRate));
									bRedWantedColor = (BYTE)(((FLOAT)bRedWantedColor * bRedRate));

									if (bBlueWantedColor > (m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift))
										bBlueWantedColor = (BYTE)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift);
									if (bGreenWantedColor > (m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift))
										bGreenWantedColor = (BYTE)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift);
									if (bRedWantedColor > (m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift))
										bRedWantedColor = (BYTE)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift);


									wPixel = pwdDst[((nYCnt + nY) * lWidth) + (nLastWidth + nX + nCheck)];
									bBlueDst = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenDst = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedDst = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									bBlueDst = (BYTE)((bOpa * (bBlueDst - bBlueWantedColor) + 100 * bBlueWantedColor) / 100);
									bGreenDst = (BYTE)((bOpa * (bGreenDst - bGreenWantedColor) + 100 * bGreenWantedColor) / 100);
									bRedDst = (BYTE)((bOpa * (bRedDst - bRedWantedColor) + 100 * bRedWantedColor) / 100);

									pwdDst[((nYCnt + nY) * lWidth) + (nLastWidth + nX + nCheck)] = ((bRedDst << m_stBitsMaskInfo.bRShift) |
										(bGreenDst << m_stBitsMaskInfo.bGShift) |
										(bBlueDst << m_stBitsMaskInfo.bBShift));
								}
								x += nCntCopyWord;
							}
							else
							{
								for (INT nCheck = 0; nCheck < nCntCopyWord; nCheck++)
								{
									wPixel = wChooseColor;
									bBlueWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									wPixel = pwSrc[x + nCheck];
									bBlueSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									rBlueRate = (FLOAT)((FLOAT)bBlueSrc / (FLOAT)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));
									rGreenRate = (FLOAT)((FLOAT)bGreenSrc / (FLOAT)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
									bRedRate = (FLOAT)((FLOAT)bRedSrc / (FLOAT)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));

									bBlueWantedColor = (BYTE)(((FLOAT)bBlueWantedColor * rBlueRate));
									bGreenWantedColor = (BYTE)(((FLOAT)bGreenWantedColor * rGreenRate));
									bRedWantedColor = (BYTE)(((FLOAT)bRedWantedColor * bRedRate));

									if (bBlueWantedColor > (m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift))
										bBlueWantedColor = (BYTE)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift);
									if (bGreenWantedColor > (m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift))
										bGreenWantedColor = (BYTE)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift);
									if (bRedWantedColor > (m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift))
										bRedWantedColor = (BYTE)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift);


									wPixel = pwdDst[((nYCnt + nY) * lWidth) + (nLastWidth + nX + nCheck)];
									bBlueDst = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenDst = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedDst = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									bBlueDst = (BYTE)((bOpa * (bBlueDst - bBlueWantedColor) + 100 * bBlueWantedColor) / 100);
									bGreenDst = (BYTE)((bOpa * (bGreenDst - bGreenWantedColor) + 100 * bGreenWantedColor) / 100);
									bRedDst = (BYTE)((bOpa * (bRedDst - bRedWantedColor) + 100 * bRedWantedColor) / 100);

									pwdDst[((nYCnt + nY) * lWidth) + (nLastWidth + nX + nCheck)] = ((bRedDst << m_stBitsMaskInfo.bRShift) |
										(bGreenDst << m_stBitsMaskInfo.bGShift) |
										(bBlueDst << m_stBitsMaskInfo.bBShift));
								}
								x += nCntCopyWord;
							}
						}
					}
					else
					{
						// DirectX 9.0 Ǩ�ƣ�ʹ��UnlockRect���Unlock
						m_pddsOffscreenSurface->UnlockRect();
						return FALSE;
					}
				}
				// ���е�ĩβ�ƶ�����һ�еĿ�ͷ��
				nWidthEnd++;

				nWidthStart = nWidthEnd;
				nCurrWidth = 0;
			}
		}

		// DirectX 9.0 Ǩ�ƣ�ʹ��UnlockRect���Unlock
		m_pddsOffscreenSurface->UnlockRect();
		return TRUE;
	}
	return FALSE;
}



BOOL CWHDXGraphicWindow::DrawWithABlendCompDataWithBackBufferEx(INT nX, INT nY,
	INT nXSize, INT nYSize, WORD* pwSrc,
	WORD wClipWidth, WORD wClipHeight,
	WORD wChooseColor1, WORD wChooseColor2, BYTE bOpa)
{
	RECT			rc;
	int				imgW;
	int				imgH;

	if (IS_OUTSIDE(nX, nY, nXSize, nYSize))
		return TRUE;

	CREATE_CLIP_REGION(nX, nY, nXSize, nYSize);

	D3DLOCKED_RECT lockedRect;
	LONG	lWidth;

	if (FAILED(m_pddsOffscreenSurface->LockRect(&lockedRect, NULL, 0)))
	{
		return FALSE;
	}

	WORD* pwdDst = (WORD*)lockedRect.pBits;
	lWidth = lockedRect.Pitch >> 1;

	WORD* dst = (WORD*)lockedRect.pBits + (nY * lWidth) + nX;
	WORD* src = (WORD*)pwSrc;
	WORD* nxt, * tmp;

	int   y, pixelCnt, pixelPos, curPos;
	WORD  cc;				// choosecolor
	BYTE  Rc, Gc, Bc;		// choosecolor's RGB
	BYTE  Rs, Gs, Bs;		// sourcecolor's RGB
	float Rr, Gr, Br;		// sourcecolor's RGB Rate
	WORD  finalColor;		// final RGB

	// Y��ü�
	for (y = 0; y < rc.top; y++)
		src += *src + 1;

	for (y = nY; y < nY + imgH; y++)
	{
		nxt = src + *src++ + 1;
		curPos = 0;

		while (src < nxt)
		{
			if (*src == 0xC0)
			{
				++src;
				curPos += *src++;
			}
			else if (*src == 0xC1)
			{
				++src;
				pixelCnt = *src++;

				if (curPos + pixelCnt > rc.left && curPos < rc.right)
				{
					// ���ü�
					if (curPos < rc.left)
					{
						src += (rc.left - curPos);
						pixelCnt -= (rc.left - curPos);
						curPos = rc.left;
					}

					// �Ҳ�ü�
					if (curPos + pixelCnt > rc.right)
						putAlpha32(dst + curPos - rc.left, src, rc.right - curPos, bOpa);
					else
						putAlpha32(dst + curPos - rc.left, src, pixelCnt, bOpa);
				}

				src += pixelCnt;
				curPos += pixelCnt;
			}
			else if (*src == 0xC2 || *src == 0xC3)
			{
				cc = *src == 0xC2 ? wChooseColor1 : wChooseColor2;

				// Ⱦɫ RGB
				Rc = (BYTE)((cc & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);
				Gc = (BYTE)((cc & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
				Bc = (BYTE)((cc & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);

				++src;
				pixelCnt = *src++;


				if (curPos + pixelCnt > rc.left && curPos < rc.right)
				{
					// ���ü�
					if (curPos < rc.left)
					{
						src += (rc.left - curPos);
						pixelCnt -= (rc.left - curPos);
						curPos = rc.left;
					}

					tmp = dst + curPos - rc.left;

					for (pixelPos = 0; pixelPos < pixelCnt; pixelPos++)
					{
						// �Ҳ�ü�
						if (curPos + pixelPos > rc.right)
							break;

						cc = src[pixelPos];

						// ԭʼ RGB
						Rs = (BYTE)((cc & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);
						Gs = (BYTE)((cc & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
						Bs = (BYTE)((cc & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);

						// ԭʼ RGB ����
						Rr = (float)((float)Rs / (float)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));
						Gr = (float)((float)Gs / (float)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
						Br = (float)((float)Bs / (float)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));

						// Ⱦɫ������ RGB
						Rs = (BYTE)((float)Rc * Rr);
						Gs = (BYTE)((float)Gc * Gr);
						Bs = (BYTE)((float)Bc * Br);

						finalColor = Rs << m_stBitsMaskInfo.bRShift |
							Gs << m_stBitsMaskInfo.bGShift |
							Bs << m_stBitsMaskInfo.bBShift;

						putAlpha32Pixel(tmp++, finalColor, bOpa);
					}
				}

				src += pixelCnt;
				curPos += pixelCnt;
			}
			else
			{
				// DirectX 9.0 Ǩ�ƣ�ʹ��UnlockRect���Unlock
				m_pddsOffscreenSurface->UnlockRect();
				return FALSE;
			}
		}

		dst += lWidth;
	}

	// DirectX 9.0 Ǩ�ƣ�ʹ��UnlockRect���Unlock
	m_pddsOffscreenSurface->UnlockRect();

	return TRUE;
}


BOOL CWHDXGraphicWindow::DrawWithShadowABlend(INT nX, INT nY, INT nXSize, INT nYSize, WORD* pwSrc, WORD wClipWidth, WORD wClipHeight,
	WORD* pwShadowClrSrc, BOOL bBlend, BYTE bShadowType, BYTE bOpa)

{
	RECT			rc;

	INT	nXOffset = 0;
	INT	nYOffset = 0;
	INT	nStartX = 0;
	INT	nStartY = 0;
	INT	nWidth = nXSize;
	INT	nHeight = nYSize;
	INT	nEndX = wClipWidth - 1;
	INT	nEndY = wClipHeight - 1;

	// �����Ƶ���Ӱ��
	if (bShadowType == 48)
	{
		return FALSE;
	}

	if (m_pddsOffscreenSurface != NULL)
	{
		rc.left = 0;
		rc.top = 0;
		rc.right = nWidth;
		rc.bottom = nHeight;

		D3DLOCKED_RECT lockedRect;
		LONG	lWidth;

		if (FAILED(m_pddsOffscreenSurface->LockRect(&lockedRect, NULL, 0)))
		{
			return FALSE;
		}

		WORD* pwdDst = (WORD*)lockedRect.pBits;
		lWidth = lockedRect.Pitch >> 1;

		INT nWidthStart = 0;
		INT nWidthEnd = 0;
		INT nCurrWidth = 0;
		INT nCntCopyWord = 0;
		INT nYCnt = 0;
		INT nLastWidth = 0;
		INT nDstXPos = 0;
		INT nDstYPos = 0;

		RECT rcScrnShadow;

		if (bShadowType == 49)
		{
			SetRect(&rcScrnShadow, nX, nY, nX + nHeight / 2 + nWidth, nY + nHeight / 2);
		}
		else if (bShadowType == 50)
		{
			SetRect(&rcScrnShadow, nX, nY, nX + nWidth, nY + nHeight);
		}

		// ȷ�� Y ����ʵ��ѭ���� Count��
		for (nYCnt = rc.top; nYCnt < rc.bottom; nYCnt++)
		{
			// ��ȡһ�еĳ��ȣ���λΪ�֣���
			nWidthEnd += pwSrc[nWidthStart];
			nWidthStart++;

			if (bShadowType == 49)
			{
				nDstYPos = nYCnt - nYCnt / 2 + nY;
			}
			else if (bShadowType == 50)
			{
				nDstYPos = nYCnt + nY;
			}

			if (nDstYPos < 0 || nDstYPos >= wClipHeight || (bShadowType == 49 && nYCnt % 2))
			{
				// ���е�ĩβ�ƶ�����һ�еĿ�ͷ��
				nWidthEnd++;
				nWidthStart = nWidthEnd;
				nCurrWidth = 0;
				continue;
			}

			// �����еĳ�������Ļ����ʾ��
			for (INT x = nWidthStart; x < nWidthEnd; )
			{
				if (pwSrc[x] == 0xC0)
				{
					x++;
					nCntCopyWord = pwSrc[x];
					x++;
					nCurrWidth += nCntCopyWord;
				}
				else if (pwSrc[x] == 0xC1 || pwSrc[x] == 0xC2 || pwSrc[x] == 0xC3)
				{
					x++;
					nCntCopyWord = pwSrc[x];
					x++;

					nLastWidth = nCurrWidth;
					nCurrWidth += nCntCopyWord;

					if (bShadowType == 49)
					{
						nDstXPos = (nLastWidth + nX + (rc.bottom - nYCnt) / 2);
					}
					else if (bShadowType == 50)
					{
						nDstXPos = (nLastWidth + nX);
					}

					if (!bBlend)
					{
						// ��౻��ȡʱ��
						if (nDstXPos < 0 && nDstXPos + nCntCopyWord >= 0)
						{
							for (INT nCheck = 0; nCheck < nCntCopyWord; nCheck++)
							{
								if (nDstXPos + nCheck >= 0 && nDstXPos + nCheck < wClipWidth)
								{
									pwdDst[(nDstYPos * lWidth) + nDstXPos + nCheck] = *pwShadowClrSrc;
								}
							}
							x += nCntCopyWord;
						}
						// �Ҳ౻��ȡʱ��
						else if (nDstXPos < wClipWidth && nDstXPos + nCntCopyWord >= wClipWidth)
						{
							for (INT nCheck = 0; nCheck < nCntCopyWord; nCheck++)
							{
								if (nDstXPos + nCheck >= 0 && nDstXPos + nCheck < wClipWidth)
								{
									pwdDst[(nDstYPos * lWidth) + nDstXPos + nCheck] = *pwShadowClrSrc;

								}
							}
							x += nCntCopyWord;
						}
						// δ����ȡʱ��
						else if (nDstXPos >= 0 && nDstXPos + nCntCopyWord < wClipWidth)
						{
							memcpy(&pwdDst[(nDstYPos * lWidth) + nDstXPos], pwShadowClrSrc, sizeof(WORD) * nCntCopyWord);
							x += nCntCopyWord;
						}
						else
						{
							x += nCntCopyWord;
						}
					}
					else
					{
						// ��౻��ȡʱ��
						if (nDstXPos < 0 && nDstXPos + nCntCopyWord >= 0)
						{
							if ((nDstYPos * lWidth) + nDstXPos < 0)
							{
								return	FALSE;
							}
							putAlpha32(&pwdDst[(nDstYPos * lWidth) + nDstXPos], pwShadowClrSrc, nCntCopyWord, bOpa);

							x += nCntCopyWord;
						}
						// �Ҳ౻��ȡʱ��
						else if (nDstXPos < wClipWidth && nDstXPos + nCntCopyWord >= wClipWidth)
						{
							putAlpha32(&pwdDst[(nDstYPos * lWidth) + nDstXPos], pwShadowClrSrc, nDstXPos + nCntCopyWord - wClipWidth, bOpa);

							x += nCntCopyWord;
						}
						// δ����ȡʱ��
						else if (nDstXPos >= 0 && nDstXPos + nCntCopyWord < wClipWidth)
						{
							putAlpha32(&pwdDst[(nDstYPos * lWidth) + nDstXPos], pwShadowClrSrc, nCntCopyWord, bOpa);

							x += nCntCopyWord;
						}
						else
						{
							x += nCntCopyWord;
						}
					}
				}
				else
				{
					// DirectX 9.0 Ǩ�ƣ�ʹ��UnlockRect���Unlock
					m_pddsOffscreenSurface->UnlockRect();

					// DirectX 9.0 Ǩ�ƣ�����������Ⱦ
					EndRender();

					return FALSE;
				}
			}
			// ���е�ĩβ�ƶ�����һ�еĿ�ͷ��
			nWidthEnd++;

			nWidthStart = nWidthEnd;
			nCurrWidth = 0;
		}

		// DirectX 9.0 Ǩ�ƣ�ʹ��UnlockRect���Unlock
		m_pddsOffscreenSurface->UnlockRect();
		return TRUE;
	}
	return FALSE;
}


VOID CWHDXGraphicWindow::DrawWithShadowABlend(INT nX, INT nY, INT nXSize, INT nYSize, INT nPX, INT nPY, WORD* pwSrc, WORD wClipWidth, WORD wClipHeight,
	WORD* pwShadowClrSrc, BOOL bBlend, BYTE bOpa)
{
	// ������Ļ��800*600���ϵĵ�Ԫ����ʼ���꣬����ʵ�����ĵ����꣨��Ԫ��������ĵ㣩��

	INT	  nScrnCenterX = nX - nPX + 24;
	INT	  nScrnCenterY = nY - nPY + 16;
	INT	  nRealCenterX = nX - nScrnCenterX;
	INT	  nRealCenterY = nY - nScrnCenterY;
	INT	  nShadowCenterX = nX;
	INT	  nShadowCenterY = nY - nRealCenterY / 2;

	RECT  rcSrc = { 0, 0, nXSize, nYSize };
	WORD wSahdowColor = pwShadowClrSrc[0];

	if (m_pddsOffscreenSurface != NULL)
	{
		D3DLOCKED_RECT lockedRect;
		LONG	lWidth;

		if (FAILED(m_pddsOffscreenSurface->LockRect(&lockedRect, NULL, 0)))
		{
			return;
		}

		WORD* pwdDst = (WORD*)lockedRect.pBits;
		lWidth = lockedRect.Pitch >> 1;

		if (pwdDst)
		{
			INT nWidthStart = 0;
			INT nWidthEnd = 0;
			INT nCurrWidth = 0;
			INT nLastWidth = 0;
			INT nCntCopyWord = 0;
			INT nYCnt = 0;

			INT nDstYPos = nShadowCenterY;

			for (nYCnt = rcSrc.top; nYCnt < rcSrc.bottom; nYCnt++)
			{
				if (nDstYPos >= wClipHeight)
				{
					// DirectX 9.0 Ǩ�ƣ�ʹ��UnlockRect���Unlock
					m_pddsOffscreenSurface->UnlockRect();
					return;
				}
				// Y��ü�������������
				if (nYCnt % 2 == 0 || nDstYPos < 0)
				{
					nWidthEnd += pwSrc[nWidthStart];
					nWidthStart++;
					nWidthEnd++;
					nWidthStart = nWidthEnd;

					nDstYPos++;
				}
				else
				{
					// ��ȡһ�еĳ��ȣ���λΪ�֣���
					nWidthEnd += pwSrc[nWidthStart];
					nWidthStart++;

					// �����еĳ�������Ļ����ʾ��
					for (INT x = nWidthStart; x < nWidthEnd; )
					{
						if (pwSrc[x] == 0xC0)
						{
							x++;
							nCntCopyWord = pwSrc[x];
							x++;
							nCurrWidth += nCntCopyWord;
						}
						else
						{
							x++;
							nCntCopyWord = pwSrc[x];
							x++;
							nLastWidth = nCurrWidth;
							nCurrWidth += nCntCopyWord;

							if (bBlend)
							{
								if ((nShadowCenterX + nLastWidth + ((-nRealCenterY / 2) - nYCnt / 2)) < 0 && (nShadowCenterX + nCurrWidth + ((-nRealCenterY / 2) - nYCnt / 2)) >= 0)
								{
								}
								else if ((nShadowCenterX + nLastWidth + ((-nRealCenterY / 2) - nYCnt / 2)) < wClipWidth && (nShadowCenterX + nCurrWidth + ((-nRealCenterY / 2) - nYCnt / 2)) >= wClipWidth)
								{
								}
								else if ((nShadowCenterX + nCurrWidth + ((-nRealCenterY / 2) - nYCnt / 2)) < 0)
								{
								}
								else if ((nShadowCenterX + nCurrWidth + ((-nRealCenterY / 2) - nYCnt / 2)) >= wClipWidth)
								{
								}
								else
								{
									putAlpha32(&pwdDst[nDstYPos * lWidth + (nShadowCenterX + nLastWidth + ((-nRealCenterY / 2) - nYCnt / 2))], pwShadowClrSrc, nCntCopyWord, bOpa);
								}
							}
							else
							{
								if ((nShadowCenterX + nLastWidth + ((-nRealCenterY / 2) - nYCnt / 2)) < 0 && (nShadowCenterX + nCurrWidth + ((-nRealCenterY / 2) - nYCnt / 2)) >= 0)
								{
								}
								else if ((nShadowCenterX + nLastWidth + ((-nRealCenterY / 2) - nYCnt / 2)) < wClipWidth && (nShadowCenterX + nCurrWidth + ((-nRealCenterY / 2) - nYCnt / 2)) >= wClipWidth)
								{
								}
								else if ((nShadowCenterX + nCurrWidth + ((-nRealCenterY / 2) - nYCnt / 2)) < 0)
								{
								}
								else if ((nShadowCenterX + nCurrWidth + ((-nRealCenterY / 2) - nYCnt / 2)) >= wClipWidth)
								{
								}
								else
								{
									memcpy(&pwdDst[nDstYPos * lWidth + (nShadowCenterX + nLastWidth + ((-nRealCenterY / 2) - nYCnt / 2))], pwShadowClrSrc, sizeof(WORD) * nCntCopyWord);
								}
							}
							x += nCntCopyWord;
						}
					}
					// ���е�ĩβ�ƶ�����һ�еĿ�ͷ��
					nWidthEnd++;

					nWidthStart = nWidthEnd;
					nCurrWidth = 0;
				}
			}
			// DirectX 9.0 Ǩ�ƣ�ʹ��UnlockRect���Unlock
			m_pddsOffscreenSurface->UnlockRect();
		}
	}
}


BOOL CWHDXGraphicWindow::DrawWithABlendCompDataWithLightedColor(INT nX, INT nY,
	INT nXSize, INT nYSize, WORD* pwSrc,
	WORD wClipWidth, WORD wClipHeight,
	WORD wChooseColor1, WORD wChooseColor2)
{
	RECT			rc;

	INT	nWidth = nXSize;
	INT	nHeight = nYSize;
	INT	nXOffset = 0;
	INT	nYOffset = 0;
	INT	nStartX = 0;
	INT	nStartY = 0;
	INT	nEndX = wClipWidth - 1;
	INT	nEndY = wClipHeight - 1;

	FLOAT fBright = 0.5f;

	if (m_pddsOffscreenSurface != NULL)
	{
		if (nX < nStartX)
		{
			nXOffset = nStartX - nX;
			nWidth = nXSize - nXOffset;
		}
		if ((nX + nXSize - 1) > nEndX)
			nWidth = nEndX - nX - nXOffset + 1;
		if (nY < nStartY)
		{
			nYOffset = nStartY - nY;
			nHeight = nYSize - nYOffset;
		}
		if ((nY + nYSize - 1) > nEndY)
			nHeight = nEndY - nY - nYOffset + 1;

		if ((nWidth > 0) && (nHeight > 0))
		{
			rc.left = nXOffset;
			rc.right = nXOffset + nWidth;
			rc.top = nYOffset;
			rc.bottom = nYOffset + nHeight;

			D3DLOCKED_RECT lockedRect;
			LONG	lWidth;

			if (FAILED(m_pddsOffscreenSurface->LockRect(&lockedRect, NULL, 0)))
			{
				return FALSE;
			}

			WORD* pwdDst = (WORD*)lockedRect.pBits;
			lWidth = lockedRect.Pitch >> 1;

			INT nWidthStart = 0;
			INT nWidthEnd = 0;
			INT nCurrWidth = 0;
			INT nCntCopyWord = 0;
			INT nYCnt = 0;
			INT nLastWidth = 0;

			BYTE  bRedWantedColor, bGreenWantedColor, bBlueWantedColor;
			BYTE bRedSrc, bGreenSrc, bBlueSrc;
			WORD wPixel;
			WORD wDyingKind, wChooseColor;
			FLOAT rBlueRate, rGreenRate, bRedRate;

			// Y��ü���
			for (nYCnt = 0; nYCnt < rc.top; nYCnt++)
			{
				nWidthEnd += pwSrc[nWidthStart];
				nWidthStart++;
				nWidthEnd++;
				nWidthStart = nWidthEnd;
			}

			// ȷ�� Y ����ʵ��ѭ���� Count��
			for (nYCnt = rc.top; nYCnt < rc.bottom; nYCnt++)
			{
				// ��ȡһ�еĳ��ȣ���λΪ�֣���
				nWidthEnd += pwSrc[nWidthStart];
				nWidthStart++;

				// �����еĳ�������Ļ����ʾ��
				for (INT x = nWidthStart; x < nWidthEnd; )
				{
					if (pwSrc[x] == 0xC0)
					{
						x++;
						nCntCopyWord = pwSrc[x];
						x++;
						nCurrWidth += nCntCopyWord;
					}
					else if (pwSrc[x] == 0xC1)
					{
						x++;
						nCntCopyWord = pwSrc[x];
						x++;

						nLastWidth = nCurrWidth;
						nCurrWidth += nCntCopyWord;

						if (rc.left > nCurrWidth || rc.right < nLastWidth)
						{
							x += nCntCopyWord;
						}
						else
						{
							// �� rc.left ��Ϊ��׼���������򱻽�ȡ�������
							if (nLastWidth < rc.left && rc.left <= nCurrWidth)
							{
								x += (rc.left - nLastWidth);
								for (INT nCheck = 0; nCheck < nCurrWidth - rc.left; nCheck++)
								{
									wPixel = pwSrc[x + nCheck];
									bBlueSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									rBlueRate = (FLOAT)((FLOAT)bBlueSrc / (FLOAT)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));
									rGreenRate = (FLOAT)((FLOAT)bGreenSrc / (FLOAT)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
									bRedRate = (FLOAT)((FLOAT)bRedSrc / (FLOAT)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));

									bBlueSrc = (BYTE)((FLOAT)bBlueSrc + ((FLOAT)bBlueSrc * rBlueRate * fBright));
									bGreenSrc = (BYTE)((FLOAT)bGreenSrc + ((FLOAT)bGreenSrc * rGreenRate * fBright));
									bRedSrc = (BYTE)((FLOAT)bRedSrc + ((FLOAT)bRedSrc * bRedRate * fBright));

									if (bBlueSrc > (m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift))
										bBlueSrc = (BYTE)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift);
									if (bGreenSrc > (m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift))
										bGreenSrc = (BYTE)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift);
									if (bRedSrc > (m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift))
										bRedSrc = (BYTE)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift);

									if (((nYCnt + nY) * lWidth) + (nLastWidth + nX + nCheck) < 0)
										return FALSE;

									pwdDst[((nYCnt + nY) * lWidth) + (rc.left + nX + nCheck)] = ((bBlueSrc << m_stBitsMaskInfo.bBShift) |
										(bGreenSrc << m_stBitsMaskInfo.bGShift) |
										(bRedSrc << m_stBitsMaskInfo.bRShift));
								}
								x += (nCurrWidth - rc.left);
							}
							// �� rc.right ��Ϊ��׼���������򱻽�ȡ�������
							else if (nLastWidth <= rc.right && rc.right < nCurrWidth)
							{
								for (INT nCheck = 0; nCheck < rc.right - nLastWidth; nCheck++)
								{
									wPixel = pwSrc[x + nCheck];
									bBlueSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									rBlueRate = (FLOAT)((FLOAT)bBlueSrc / (FLOAT)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));
									rGreenRate = (FLOAT)((FLOAT)bGreenSrc / (FLOAT)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
									bRedRate = (FLOAT)((FLOAT)bRedSrc / (FLOAT)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));

									bBlueSrc = (BYTE)((FLOAT)bBlueSrc + ((FLOAT)bBlueSrc * rBlueRate * fBright));
									bGreenSrc = (BYTE)((FLOAT)bGreenSrc + ((FLOAT)bGreenSrc * rGreenRate * fBright));
									bRedSrc = (BYTE)((FLOAT)bRedSrc + ((FLOAT)bRedSrc * bRedRate * fBright));

									if (bBlueSrc > (m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift))
										bBlueSrc = (BYTE)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift);
									if (bGreenSrc > (m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift))
										bGreenSrc = (BYTE)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift);
									if (bRedSrc > (m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift))
										bRedSrc = (BYTE)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift);

									pwdDst[((nYCnt + nY) * lWidth) + (nLastWidth + nX + nCheck)] = ((bBlueSrc << m_stBitsMaskInfo.bBShift) |
										(bGreenSrc << m_stBitsMaskInfo.bGShift) |
										(bRedSrc << m_stBitsMaskInfo.bRShift));
								}
								x += nCntCopyWord;
							}
							else
							{
								for (INT nCheck = 0; nCheck < nCntCopyWord; nCheck++)
								{
									wPixel = pwSrc[x + nCheck];
									bBlueSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									rBlueRate = (FLOAT)((FLOAT)bBlueSrc / (FLOAT)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));
									rGreenRate = (FLOAT)((FLOAT)bGreenSrc / (FLOAT)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
									bRedRate = (FLOAT)((FLOAT)bRedSrc / (FLOAT)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));

									bBlueSrc = (BYTE)((FLOAT)bBlueSrc + ((FLOAT)bBlueSrc * rBlueRate * fBright));
									bGreenSrc = (BYTE)((FLOAT)bGreenSrc + ((FLOAT)bGreenSrc * rGreenRate * fBright));
									bRedSrc = (BYTE)((FLOAT)bRedSrc + ((FLOAT)bRedSrc * bRedRate * fBright));

									if (bBlueSrc > (m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift))
										bBlueSrc = (BYTE)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift);
									if (bGreenSrc > (m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift))
										bGreenSrc = (BYTE)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift);
									if (bRedSrc > (m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift))
										bRedSrc = (BYTE)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift);

									pwdDst[((nYCnt + nY) * lWidth) + (nLastWidth + nX + nCheck)] = ((bBlueSrc << m_stBitsMaskInfo.bBShift) |
										(bGreenSrc << m_stBitsMaskInfo.bGShift) |
										(bRedSrc << m_stBitsMaskInfo.bRShift));
								}
								x += nCntCopyWord;
							}
						}
					}
					else if (pwSrc[x] == 0xC2 || pwSrc[x] == 0xC3)
					{
						wDyingKind = pwSrc[x];
						switch (wDyingKind)
						{
						case 0xC2:
							wChooseColor = wChooseColor1;
							break;
						case 0xC3:
							wChooseColor = wChooseColor2;
							break;
						}
						x++;
						nCntCopyWord = pwSrc[x];
						x++;

						nLastWidth = nCurrWidth;
						nCurrWidth += nCntCopyWord;

						if (rc.left > nCurrWidth || rc.right < nLastWidth)
						{
							x += nCntCopyWord;
						}
						else
						{
							// �� rc.left ��Ϊ��׼���������򱻽�ȡ�������
							if (nLastWidth < rc.left && rc.left <= nCurrWidth)
							{
								x += (rc.left - nLastWidth);
								for (INT nCheck = 0; nCheck < nCurrWidth - rc.left; nCheck++)
								{
									wPixel = wChooseColor;
									bBlueWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									wPixel = pwSrc[x + nCheck];
									bBlueSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									rBlueRate = (FLOAT)((FLOAT)bBlueSrc / (FLOAT)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));
									rGreenRate = (FLOAT)((FLOAT)bGreenSrc / (FLOAT)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
									bRedRate = (FLOAT)((FLOAT)bRedSrc / (FLOAT)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));

									bBlueWantedColor = (BYTE)(((FLOAT)bBlueWantedColor * rBlueRate));
									bGreenWantedColor = (BYTE)(((FLOAT)bGreenWantedColor * rGreenRate));
									bRedWantedColor = (BYTE)(((FLOAT)bRedWantedColor * bRedRate));

									if (bBlueWantedColor > (m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift))
										bBlueWantedColor = (BYTE)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift);
									if (bGreenWantedColor > (m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift))
										bGreenWantedColor = (BYTE)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift);
									if (bRedWantedColor > (m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift))
										bRedWantedColor = (BYTE)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift);

									bBlueSrc = bBlueWantedColor;
									bGreenSrc = bGreenWantedColor;
									bRedSrc = bRedWantedColor;

									rBlueRate = (FLOAT)((FLOAT)bBlueSrc / (FLOAT)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));
									rGreenRate = (FLOAT)((FLOAT)bGreenSrc / (FLOAT)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
									bRedRate = (FLOAT)((FLOAT)bRedSrc / (FLOAT)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));

									bBlueSrc = (BYTE)((FLOAT)bBlueSrc + ((FLOAT)bBlueSrc * rBlueRate * fBright));
									bGreenSrc = (BYTE)((FLOAT)bGreenSrc + ((FLOAT)bGreenSrc * rGreenRate * fBright));
									bRedSrc = (BYTE)((FLOAT)bRedSrc + ((FLOAT)bRedSrc * bRedRate * fBright));

									if (bBlueSrc > (m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift))
										bBlueSrc = (BYTE)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift);
									if (bGreenSrc > (m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift))
										bGreenSrc = (BYTE)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift);
									if (bRedSrc > (m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift))
										bRedSrc = (BYTE)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift);

									if (((nYCnt + nY) * lWidth) + (rc.left + nX + nCheck) < 0)
										return FALSE;

									pwdDst[((nYCnt + nY) * lWidth) + (rc.left + nX + nCheck)] = ((bBlueSrc << m_stBitsMaskInfo.bBShift) |
										(bGreenSrc << m_stBitsMaskInfo.bGShift) |
										(bRedSrc << m_stBitsMaskInfo.bRShift));
								}
								x += (nCurrWidth - rc.left);
							}
							// �� rc.right ��Ϊ��׼���������򱻽�ȡ�������
							else if (nLastWidth <= rc.right && rc.right < nCurrWidth)
							{
								for (INT nCheck = 0; nCheck < rc.right - nLastWidth; nCheck++)
								{
									wPixel = wChooseColor;
									bBlueWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									wPixel = pwSrc[x + nCheck];
									bBlueSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									rBlueRate = (FLOAT)((FLOAT)bBlueSrc / (FLOAT)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));
									rGreenRate = (FLOAT)((FLOAT)bGreenSrc / (FLOAT)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
									bRedRate = (FLOAT)((FLOAT)bRedSrc / (FLOAT)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));

									bBlueWantedColor = (BYTE)(((FLOAT)bBlueWantedColor * rBlueRate));
									bGreenWantedColor = (BYTE)(((FLOAT)bGreenWantedColor * rGreenRate));
									bRedWantedColor = (BYTE)(((FLOAT)bRedWantedColor * bRedRate));

									if (bBlueWantedColor > (m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift))
										bBlueWantedColor = (BYTE)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift);
									if (bGreenWantedColor > (m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift))
										bGreenWantedColor = (BYTE)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift);
									if (bRedWantedColor > (m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift))
										bRedWantedColor = (BYTE)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift);

									bBlueSrc = bBlueWantedColor;
									bGreenSrc = bGreenWantedColor;
									bRedSrc = bRedWantedColor;

									rBlueRate = (FLOAT)((FLOAT)bBlueSrc / (FLOAT)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));
									rGreenRate = (FLOAT)((FLOAT)bGreenSrc / (FLOAT)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
									bRedRate = (FLOAT)((FLOAT)bRedSrc / (FLOAT)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));

									bBlueSrc = (BYTE)((FLOAT)bBlueSrc + ((FLOAT)bBlueSrc * rBlueRate * fBright));
									bGreenSrc = (BYTE)((FLOAT)bGreenSrc + ((FLOAT)bGreenSrc * rGreenRate * fBright));
									bRedSrc = (BYTE)((FLOAT)bRedSrc + ((FLOAT)bRedSrc * bRedRate * fBright));

									if (bBlueSrc > (m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift))
										bBlueSrc = (BYTE)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift);
									if (bGreenSrc > (m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift))
										bGreenSrc = (BYTE)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift);
									if (bRedSrc > (m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift))
										bRedSrc = (BYTE)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift);

									pwdDst[((nYCnt + nY) * lWidth) + (nLastWidth + nX + nCheck)] = ((bBlueSrc << m_stBitsMaskInfo.bBShift) |
										(bGreenSrc << m_stBitsMaskInfo.bGShift) |
										(bRedSrc << m_stBitsMaskInfo.bRShift));
								}
								x += nCntCopyWord;
							}
							else
							{
								for (INT nCheck = 0; nCheck < nCntCopyWord; nCheck++)
								{
									wPixel = wChooseColor;
									bBlueWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									wPixel = pwSrc[x + nCheck];
									bBlueSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									rBlueRate = (FLOAT)((FLOAT)bBlueSrc / (FLOAT)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));
									rGreenRate = (FLOAT)((FLOAT)bGreenSrc / (FLOAT)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
									bRedRate = (FLOAT)((FLOAT)bRedSrc / (FLOAT)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));

									bBlueWantedColor = (BYTE)(((FLOAT)bBlueWantedColor * rBlueRate));
									bGreenWantedColor = (BYTE)(((FLOAT)bGreenWantedColor * rGreenRate));
									bRedWantedColor = (BYTE)(((FLOAT)bRedWantedColor * bRedRate));

									if (bBlueWantedColor > (m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift))
										bBlueWantedColor = (BYTE)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift);
									if (bGreenWantedColor > (m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift))
										bGreenWantedColor = (BYTE)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift);
									if (bRedWantedColor > (m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift))
										bRedWantedColor = (BYTE)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift);

									bBlueSrc = bBlueWantedColor;
									bGreenSrc = bGreenWantedColor;
									bRedSrc = bRedWantedColor;

									rBlueRate = (FLOAT)((FLOAT)bBlueSrc / (FLOAT)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));
									rGreenRate = (FLOAT)((FLOAT)bGreenSrc / (FLOAT)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
									bRedRate = (FLOAT)((FLOAT)bRedSrc / (FLOAT)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));

									bBlueSrc = (BYTE)((FLOAT)bBlueSrc + ((FLOAT)bBlueSrc * rBlueRate * fBright));
									bGreenSrc = (BYTE)((FLOAT)bGreenSrc + ((FLOAT)bGreenSrc * rGreenRate * fBright));
									bRedSrc = (BYTE)((FLOAT)bRedSrc + ((FLOAT)bRedSrc * bRedRate * fBright));

									if (bBlueSrc > (m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift))
										bBlueSrc = (BYTE)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift);
									if (bGreenSrc > (m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift))
										bGreenSrc = (BYTE)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift);
									if (bRedSrc > (m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift))
										bRedSrc = (BYTE)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift);

									pwdDst[((nYCnt + nY) * lWidth) + (nLastWidth + nX + nCheck)] = ((bBlueSrc << m_stBitsMaskInfo.bBShift) |
										(bGreenSrc << m_stBitsMaskInfo.bGShift) |
										(bRedSrc << m_stBitsMaskInfo.bRShift));
								}
								x += nCntCopyWord;
							}
						}
					}
					else
					{
						// DirectX 9.0 Ǩ�ƣ�ʹ��UnlockRect���Unlock
						m_pddsOffscreenSurface->UnlockRect();
						return FALSE;
					}
				}
				// ���е�ĩβ�ƶ�����һ�еĿ�ͷ��
				nWidthEnd++;

				nWidthStart = nWidthEnd;
				nCurrWidth = 0;
			}
		}

		// DirectX 9.0 Ǩ�ƣ�ʹ��UnlockRect���Unlock
		m_pddsOffscreenSurface->UnlockRect();
		return TRUE;
	}
	return FALSE;
}



BOOL CWHDXGraphicWindow::DrawWithImageForCompClipRgnColor(INT nX, INT nY, INT nXSize, INT nYSize, WORD* pwSrc, WORD wClipWidth, WORD wClipHeight, WORD wColor, BOOL bFocused, BOOL bBlend)
{
	RECT			rc;

	INT	nWidth = nXSize;
	INT	nHeight = nYSize;
	INT	nXOffset = 0;
	INT	nYOffset = 0;
	INT	nStartX = 0;
	INT	nStartY = 0;
	INT	nEndX = wClipWidth - 1;
	INT	nEndY = wClipHeight - 1;

	if (m_pddsOffscreenSurface != NULL)
	{
		if (nX < nStartX)
		{
			nXOffset = nStartX - nX;
			nWidth = nXSize - nXOffset;
		}
		if ((nX + nXSize - 1) > nEndX)
			nWidth = nEndX - nX - nXOffset + 1;
		if (nY < nStartY)
		{
			nYOffset = nStartY - nY;
			nHeight = nYSize - nYOffset;
		}
		if ((nY + nYSize - 1) > nEndY)
			nHeight = nEndY - nY - nYOffset + 1;

		if ((nWidth > 0) && (nHeight > 0))
		{
			rc.left = nXOffset;
			rc.right = nXOffset + nWidth;
			rc.top = nYOffset;
			rc.bottom = nYOffset + nHeight;

			D3DLOCKED_RECT lockedRect;
			LONG	lWidth;

			if (FAILED(m_pddsOffscreenSurface->LockRect(&lockedRect, NULL, 0)))
			{
				return FALSE;
			}

			WORD* pwdDst = (WORD*)lockedRect.pBits;
			lWidth = lockedRect.Pitch >> 1;

			INT nWidthStart = 0;
			INT nWidthEnd = 0;
			INT nCurrWidth = 0;
			INT nCntCopyWord = 0;
			INT nYCnt = 0;
			INT nLastWidth = 0;

			BYTE  bRedStateColor, bGreenStateColor, bBlueStateColor;
			BYTE  bRedSrc, bGreenSrc, bBlueSrc;
			BYTE  bBlueDst, bGreenDst, bRedDst;
			WORD  wPixel;
			FLOAT rBlueRate, rGreenRate, rRedRate;
			FLOAT rbLightRate = 0.0f;

			// Y��ü���
			for (nYCnt = 0; nYCnt < rc.top; nYCnt++)
			{
				nWidthEnd += pwSrc[nWidthStart];
				nWidthStart++;
				nWidthEnd++;
				nWidthStart = nWidthEnd;
			}

			// ȷ�� Y ����ʵ��ѭ���� Count��
			for (nYCnt = rc.top; nYCnt < rc.bottom; nYCnt++)
			{
				// ��ȡһ�еĳ��ȣ���λΪ�֣���
				nWidthEnd += pwSrc[nWidthStart];
				nWidthStart++;

				// �����еĳ�������Ļ����ʾ��
				for (INT x = nWidthStart; x < nWidthEnd; )
				{
					if (pwSrc[x] == 0xC0)
					{
						x++;
						nCntCopyWord = pwSrc[x];
						x++;
						nCurrWidth += nCntCopyWord;
					}
					else if (pwSrc[x] == 0xC1 || pwSrc[x] == 0xC2 || pwSrc[x] == 0xC3)
					{
						x++;
						nCntCopyWord = pwSrc[x];
						x++;

						nLastWidth = nCurrWidth;
						nCurrWidth += nCntCopyWord;

						if (rc.left > nCurrWidth || rc.right < nLastWidth)
						{
							x += nCntCopyWord;
						}
						else
						{
							// �� rc.left ��Ϊ��׼���������򱻽�ȡ�������
							if (nLastWidth < rc.left && rc.left <= nCurrWidth)
							{
								x += (rc.left - nLastWidth);
								for (INT nCheck = 0; nCheck < nCurrWidth - rc.left; nCheck++)
								{
									wPixel = wColor;
									bBlueStateColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenStateColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedStateColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									wPixel = pwSrc[x + nCheck];
									bBlueSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									// ����״̬.
									if (bFocused)			rbLightRate = 0.6f;
									else					rbLightRate = 0.0f;

									if (!wColor)
									{
										BYTE bTemp = (BYTE)(bBlueSrc + (bGreenSrc >> 1) + bRedSrc) / 3;
										bTemp = bTemp + (BYTE)(bTemp * rbLightRate);
										if (bTemp > 31)	bTemp = 31;
										bBlueStateColor = bRedStateColor = bTemp;
										bGreenStateColor = bTemp << 1;
									}
									else
									{
										rBlueRate = (FLOAT)((FLOAT)bBlueSrc / (FLOAT)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));
										rGreenRate = (FLOAT)((FLOAT)bGreenSrc / (FLOAT)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
										rRedRate = (FLOAT)((FLOAT)bRedSrc / (FLOAT)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));

										bBlueStateColor = ((BYTE)((FLOAT)bBlueStateColor * rBlueRate) + (BYTE)((FLOAT)bBlueStateColor * rBlueRate * rbLightRate));
										bGreenStateColor = ((BYTE)((FLOAT)bGreenStateColor * rGreenRate) + (BYTE)((FLOAT)bGreenStateColor * rGreenRate * rbLightRate));
										bRedStateColor = ((BYTE)((FLOAT)bRedStateColor * rRedRate) + (BYTE)((FLOAT)bRedStateColor * rRedRate * rbLightRate));

										if (bBlueStateColor > (m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift))
											bBlueStateColor = (BYTE)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift);
										if (bGreenStateColor > (m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift))
											bGreenStateColor = (BYTE)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift);
										if (bRedStateColor > (m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift))
											bRedStateColor = (BYTE)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift);
									}

									if (bBlend)
									{
										wPixel = pwdDst[((nYCnt + nY) * lWidth) + (nLastWidth + nX + nCheck)];
										bBlueDst = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
										bGreenDst = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
										bRedDst = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

										bBlueDst = (BYTE)((50 * (bBlueDst - bBlueStateColor) + 100 * bBlueStateColor) / 100);
										bGreenDst = (BYTE)((50 * (bGreenDst - bGreenStateColor) + 100 * bGreenStateColor) / 100);
										bRedDst = (BYTE)((50 * (bRedDst - bRedStateColor) + 100 * bRedStateColor) / 100);
									}
									else
									{
										bBlueDst = bBlueStateColor;
										bGreenDst = bGreenStateColor;
										bRedDst = bRedStateColor;
									}

									if (((nYCnt + nY) * lWidth) + (rc.left + nX + nCheck) < 0)
										return FALSE;

									pwdDst[((nYCnt + nY) * lWidth) + (rc.left + nX + nCheck)] = ((bBlueDst << m_stBitsMaskInfo.bBShift) |
										(bGreenDst << m_stBitsMaskInfo.bGShift) |
										(bRedDst << m_stBitsMaskInfo.bRShift));
								}
								x += (nCurrWidth - rc.left);
							}
							// �� rc.right ��Ϊ��׼���������򱻽�ȡ�������
							else if (nLastWidth <= rc.right && rc.right < nCurrWidth)
							{
								for (INT nCheck = 0; nCheck < rc.right - nLastWidth; nCheck++)
								{
									wPixel = wColor;
									bBlueStateColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenStateColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedStateColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									wPixel = pwSrc[x + nCheck];
									bBlueSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									// ����״̬.
									if (bFocused)			rbLightRate = 0.6f;
									else					rbLightRate = 0.0f;

									if (!wColor)
									{
										BYTE bTemp = (BYTE)(bBlueSrc + (bGreenSrc >> 1) + bRedSrc) / 3;
										bTemp = bTemp + (BYTE)(bTemp * rbLightRate);
										if (bTemp > 31)	bTemp = 31;
										bBlueStateColor = bRedStateColor = bTemp;
										bGreenStateColor = bTemp << 1;
									}
									else
									{
										rBlueRate = (FLOAT)((FLOAT)bBlueSrc / (FLOAT)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));
										rGreenRate = (FLOAT)((FLOAT)bGreenSrc / (FLOAT)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
										rRedRate = (FLOAT)((FLOAT)bRedSrc / (FLOAT)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));

										bBlueStateColor = ((BYTE)((FLOAT)bBlueStateColor * rBlueRate) + (BYTE)((FLOAT)bBlueStateColor * rBlueRate * rbLightRate));
										bGreenStateColor = ((BYTE)((FLOAT)bGreenStateColor * rGreenRate) + (BYTE)((FLOAT)bGreenStateColor * rGreenRate * rbLightRate));
										bRedStateColor = ((BYTE)((FLOAT)bRedStateColor * rRedRate) + (BYTE)((FLOAT)bRedStateColor * rRedRate * rbLightRate));

										if (bBlueStateColor > (m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift))
											bBlueStateColor = (BYTE)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift);
										if (bGreenStateColor > (m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift))
											bGreenStateColor = (BYTE)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift);
										if (bRedStateColor > (m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift))
											bRedStateColor = (BYTE)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift);
									}

									if (bBlend)
									{
										wPixel = pwdDst[((nYCnt + nY) * lWidth) + (nLastWidth + nX + nCheck)];
										bBlueDst = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
										bGreenDst = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
										bRedDst = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

										bBlueDst = (BYTE)((50 * (bBlueDst - bBlueStateColor) + 100 * bBlueStateColor) / 100);
										bGreenDst = (BYTE)((50 * (bGreenDst - bGreenStateColor) + 100 * bGreenStateColor) / 100);
										bRedDst = (BYTE)((50 * (bRedDst - bRedStateColor) + 100 * bRedStateColor) / 100);
									}
									else
									{
										bBlueDst = bBlueStateColor;
										bGreenDst = bGreenStateColor;
										bRedDst = bRedStateColor;
									}

									pwdDst[((nYCnt + nY) * lWidth) + (nLastWidth + nX + nCheck)] = ((bBlueDst << m_stBitsMaskInfo.bRShift) |
										(bGreenDst << m_stBitsMaskInfo.bGShift) |
										(bRedDst << m_stBitsMaskInfo.bBShift));
								}
								x += nCntCopyWord;
							}
							else
							{
								for (INT nCheck = 0; nCheck < nCntCopyWord; nCheck++)
								{
									wPixel = wColor;
									bBlueStateColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenStateColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedStateColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									wPixel = pwSrc[x + nCheck];
									bBlueSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									// ����״̬.
									if (bFocused)			rbLightRate = 0.6f;
									else					rbLightRate = 0.0f;

									if (!wColor)
									{
										BYTE bTemp = (BYTE)(bBlueSrc + (bGreenSrc >> 1) + bRedSrc) / 3;
										bTemp = bTemp + (BYTE)(bTemp * rbLightRate);
										if (bTemp > 31)	bTemp = 31;
										bBlueStateColor = bRedStateColor = bTemp;
										bGreenStateColor = bTemp << 1;
									}
									else
									{
										rBlueRate = (FLOAT)((FLOAT)bBlueSrc / (FLOAT)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));
										rGreenRate = (FLOAT)((FLOAT)bGreenSrc / (FLOAT)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
										rRedRate = (FLOAT)((FLOAT)bRedSrc / (FLOAT)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));

										bBlueStateColor = ((BYTE)((FLOAT)bBlueStateColor * rBlueRate) + (BYTE)((FLOAT)bBlueStateColor * rBlueRate * rbLightRate));
										bGreenStateColor = ((BYTE)((FLOAT)bGreenStateColor * rGreenRate) + (BYTE)((FLOAT)bGreenStateColor * rGreenRate * rbLightRate));
										bRedStateColor = ((BYTE)((FLOAT)bRedStateColor * rRedRate) + (BYTE)((FLOAT)bRedStateColor * rRedRate * rbLightRate));

										if (bBlueStateColor > (m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift))
											bBlueStateColor = (BYTE)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift);
										if (bGreenStateColor > (m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift))
											bGreenStateColor = (BYTE)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift);
										if (bRedStateColor > (m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift))
											bRedStateColor = (BYTE)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift);
									}

									if (bBlend)
									{
										wPixel = pwdDst[((nYCnt + nY) * lWidth) + (nLastWidth + nX + nCheck)];
										bBlueDst = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
										bGreenDst = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
										bRedDst = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

										bBlueDst = (BYTE)((50 * (bBlueDst - bBlueStateColor) + 100 * bBlueStateColor) / 100);
										bGreenDst = (BYTE)((50 * (bGreenDst - bGreenStateColor) + 100 * bGreenStateColor) / 100);
										bRedDst = (BYTE)((50 * (bRedDst - bRedStateColor) + 100 * bRedStateColor) / 100);
									}
									else
									{
										bBlueDst = bBlueStateColor;
										bGreenDst = bGreenStateColor;
										bRedDst = bRedStateColor;
									}

									pwdDst[((nYCnt + nY) * lWidth) + (nLastWidth + nX + nCheck)] = ((bBlueDst << m_stBitsMaskInfo.bBShift) |
										(bGreenDst << m_stBitsMaskInfo.bGShift) |
										(bRedDst << m_stBitsMaskInfo.bRShift));
								}
								x += nCntCopyWord;
							}
						}
					}
					else
					{
						// DirectX 9.0 Ǩ�ƣ�ʹ��UnlockRect���Unlock
						m_pddsOffscreenSurface->UnlockRect();
						return FALSE;
					}
				}
				// ���е�ĩβ�ƶ�����һ�еĿ�ͷ��
				nWidthEnd++;

				nWidthStart = nWidthEnd;
				nCurrWidth = 0;
			}
		}

		// DirectX 9.0 Ǩ�ƣ�ʹ��UnlockRect���Unlock
		m_pddsOffscreenSurface->UnlockRect();
		return TRUE;
	}
	return FALSE;
}



BOOL CWHDXGraphicWindow::DrawWithImageForCompClipRgnColor(INT nX, INT nY, INT nXSize, INT nYSize, WORD* pwSrc, WORD wClipWidth, WORD wClipHeight, WORD wColor, WORD wChooseColor1, WORD wChooseColor2)
{
	RECT			rc;

	INT	nWidth = nXSize;
	INT	nHeight = nYSize;
	INT	nXOffset = 0;
	INT	nYOffset = 0;
	INT	nStartX = 0;
	INT	nStartY = 0;
	INT	nEndX = wClipWidth - 1;
	INT	nEndY = wClipHeight - 1;

	INT FiveMiddle = 0xf;
	INT SixMiddle = 0x1f;

	BYTE bsBlueColor, bsGreenColor, bsRedColor;
	BYTE bdBlueColor, bdGreenColor, bdRedColor;

	bdBlueColor = (BYTE)((wColor & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
	bdGreenColor = (BYTE)((wColor & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
	bdRedColor = (BYTE)((wColor & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

	if (m_pddsOffscreenSurface != NULL)
	{
		if (nX < nStartX)
		{
			nXOffset = nStartX - nX;
			nWidth = nXSize - nXOffset;
		}
		if ((nX + nXSize - 1) > nEndX)
			nWidth = nEndX - nX - nXOffset + 1;
		if (nY < nStartY)
		{
			nYOffset = nStartY - nY;
			nHeight = nYSize - nYOffset;
		}
		if ((nY + nYSize - 1) > nEndY)
			nHeight = nEndY - nY - nYOffset + 1;

		if ((nWidth > 0) && (nHeight > 0))
		{
			rc.left = nXOffset;
			rc.right = nXOffset + nWidth;
			rc.top = nYOffset;
			rc.bottom = nYOffset + nHeight;

			D3DLOCKED_RECT lockedRect;
			LONG	lWidth;

			if (FAILED(m_pddsOffscreenSurface->LockRect(&lockedRect, NULL, 0)))
			{
				return FALSE;
			}

			WORD* pwdDst = (WORD*)lockedRect.pBits;
			lWidth = lockedRect.Pitch >> 1;

			INT nWidthStart = 0;
			INT nWidthEnd = 0;
			INT nCurrWidth = 0;
			INT nCntCopyWord = 0;
			INT nYCnt = 0;
			INT nLastWidth = 0;

			FLOAT rBlueRate, rGreenRate, bRedRate;
			BYTE  bRedSrc, bGreenSrc, bBlueSrc;
			BYTE  bRedWantedColor, bGreenWantedColor, bBlueWantedColor;
			WORD  wPixel;

			// Y��ü���
			for (nYCnt = 0; nYCnt < rc.top; nYCnt++)
			{
				nWidthEnd += pwSrc[nWidthStart];
				nWidthStart++;
				nWidthEnd++;
				nWidthStart = nWidthEnd;
			}

			// ȷ�� Y ����ʵ��ѭ���� Count��
			for (nYCnt = rc.top; nYCnt < rc.bottom; nYCnt++)
			{
				// ��ȡһ�еĳ��ȣ���λΪ�֣���
				nWidthEnd += pwSrc[nWidthStart];
				nWidthStart++;

				// �����еĳ�������Ļ����ʾ��
				for (INT x = nWidthStart; x < nWidthEnd; )
				{
					if (pwSrc[x] == 0xC0)
					{
						x++;
						nCntCopyWord = pwSrc[x];
						x++;
						nCurrWidth += nCntCopyWord;
					}
					else if (pwSrc[x] == 0xC1)
					{
						x++;
						nCntCopyWord = pwSrc[x];	// ����
						x++;

						nLastWidth = nCurrWidth;
						nCurrWidth += nCntCopyWord;

						if (rc.left > nCurrWidth || rc.right < nLastWidth)
						{
							x += nCntCopyWord;		// Ī����������
						}
						else
						{
							// �� rc.left ��Ϊ��׼���������򱻽�ȡ�������
							if (nLastWidth < rc.left && rc.left <= nCurrWidth)
							{
								x += (rc.left - nLastWidth);

								for (WORD i = 0; i < (sizeof(WORD) * (nCurrWidth - rc.left) / 2); i++)
								{
									bsBlueColor = (BYTE)((pwSrc[x + i] & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bsGreenColor = (BYTE)((pwSrc[x + i] & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bsRedColor = (BYTE)((pwSrc[x + i] & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									BYTE	temp;

									temp = (bsRedColor + (bsGreenColor >> 1) + bsBlueColor) / 3;

									bsBlueColor = temp > 0 ? (temp & bdBlueColor) : 0;
									bsGreenColor = temp > 0 ? (temp & (bdGreenColor << 1)) : 0;
									bsRedColor = temp > 0 ? (temp & bdRedColor) : 0;

									if (bsBlueColor >= 32)  bsBlueColor = 31;
									if (bsGreenColor >= 64)  bsGreenColor = 63;
									if (bsRedColor >= 32)  bsRedColor = 31;

									if (((nYCnt + nY) * lWidth) + (rc.left + nX) + i < 0)
										return FALSE;

									pwdDst[((nYCnt + nY) * lWidth) + (rc.left + nX) + i] = (bsBlueColor << m_stBitsMaskInfo.bBShift) | (bsGreenColor << m_stBitsMaskInfo.bGShift) | (bsRedColor << m_stBitsMaskInfo.bRShift);
								}

								x += (nCurrWidth - rc.left);
							}
							// �� rc.right ��Ϊ��׼���������򱻽�ȡ�������
							else if (nLastWidth <= rc.right && rc.right < nCurrWidth)
							{
								for (WORD i = 0; i < (sizeof(WORD) * (rc.right - nLastWidth)); i++)
								{
									bsBlueColor = (BYTE)((pwSrc[x + i] & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bsGreenColor = (BYTE)((pwSrc[x + i] & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bsRedColor = (BYTE)((pwSrc[x + i] & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									BYTE	temp;

									temp = (bsRedColor + (bsGreenColor >> 1) + bsBlueColor) / 3;

									bsBlueColor = temp > 0 ? (temp & bdBlueColor) : 0;
									bsGreenColor = temp > 0 ? (temp & (bdGreenColor << 1)) : 0;
									bsRedColor = temp > 0 ? (temp & bdRedColor) : 0;

									if (bsBlueColor >= 32)  bsBlueColor = 31;
									if (bsGreenColor >= 64)  bsGreenColor = 63;
									if (bsRedColor >= 32)  bsRedColor = 31;

									pwdDst[((nYCnt + nY) * lWidth) + (nLastWidth + nX) + i] = (bsBlueColor << m_stBitsMaskInfo.bBShift) | (bsGreenColor << m_stBitsMaskInfo.bGShift) | (bsRedColor << m_stBitsMaskInfo.bRShift);
								}
								//								memcpy(&pwdDst[((nYCnt+nY) * lWidth) + (nLastWidth+nX)], &pwSrc[x], sizeof(WORD)*(rc.right-nLastWidth));
								x += nCntCopyWord;
							}
							else
							{
								for (WORD i = 0; i < (sizeof(WORD) * nCntCopyWord / 2); i++)
								{
									bsBlueColor = (BYTE)((pwSrc[x + i] & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bsGreenColor = (BYTE)((pwSrc[x + i] & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bsRedColor = (BYTE)((pwSrc[x + i] & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									BYTE	temp;

									temp = (bsRedColor + (bsGreenColor >> 1) + bsBlueColor) / 3;

									bsBlueColor = temp > 0 ? (temp & bdBlueColor) : 0;
									bsGreenColor = temp > 0 ? (temp & (bdGreenColor << 1)) : 0;
									bsRedColor = temp > 0 ? (temp & bdRedColor) : 0;

									if (bsBlueColor >= 32)  bsBlueColor = 31;
									if (bsGreenColor >= 64)  bsGreenColor = 63;
									if (bsRedColor >= 32)  bsRedColor = 31;

									pwdDst[((nYCnt + nY) * lWidth) + (nLastWidth + nX) + i] = (bsBlueColor << m_stBitsMaskInfo.bBShift) | (bsGreenColor << m_stBitsMaskInfo.bGShift) | (bsRedColor << m_stBitsMaskInfo.bRShift);
								}
								x += nCntCopyWord;
							}
						}
					}
					else if (pwSrc[x] == 0xC2 || pwSrc[x] == 0xC3)
					{
						WORD wDyingKind, wChooseColor;
						wDyingKind = pwSrc[x];
						switch (wDyingKind)
						{
						case 0xC2:
							wChooseColor = wChooseColor1;
							break;
						case 0xC3:
							wChooseColor = wChooseColor2;
							break;
						}
						x++;
						nCntCopyWord = pwSrc[x];
						x++;

						nLastWidth = nCurrWidth;
						nCurrWidth += nCntCopyWord;

						if (rc.left > nCurrWidth || rc.right < nLastWidth)
						{
							x += nCntCopyWord;
						}
						else
						{
							// �� rc.left ��Ϊ��׼���������򱻽�ȡ�������
							if (nLastWidth < rc.left && rc.left <= nCurrWidth)
							{
								x += (rc.left - nLastWidth);
								for (INT nCheck = 0; nCheck < nCurrWidth - rc.left; nCheck++)
								{
									wPixel = wChooseColor;
									bBlueWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									wPixel = pwSrc[x + nCheck];
									bBlueSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									rBlueRate = (FLOAT)((FLOAT)bBlueSrc / (FLOAT)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));
									rGreenRate = (FLOAT)((FLOAT)bGreenSrc / (FLOAT)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
									bRedRate = (FLOAT)((FLOAT)bRedSrc / (FLOAT)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));

									bBlueWantedColor = (BYTE)(((FLOAT)bBlueWantedColor * rBlueRate));
									bGreenWantedColor = (BYTE)(((FLOAT)bGreenWantedColor * rGreenRate));
									bRedWantedColor = (BYTE)(((FLOAT)bRedWantedColor * bRedRate));

									if (bBlueWantedColor > (m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift))
										bBlueWantedColor = (BYTE)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift);
									if (bGreenWantedColor > (m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift))
										bGreenWantedColor = (BYTE)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift);
									if (bRedWantedColor > (m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift))
										bRedWantedColor = (BYTE)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift);

									BYTE	temp;

									temp = (bsRedColor + (bGreenWantedColor >> 1) + bBlueWantedColor) / 3;

									bBlueWantedColor = temp > 0 ? (temp & bdBlueColor) : 0;
									bGreenWantedColor = temp > 0 ? (temp & (bdGreenColor << 1)) : 0;
									bRedWantedColor = temp > 0 ? (temp & bdRedColor) : 0;

									if (bBlueWantedColor >= 32)  bBlueWantedColor = 31;
									if (bGreenWantedColor >= 64)  bGreenWantedColor = 63;
									if (bRedWantedColor >= 32)  bRedWantedColor = 31;

									if (((nYCnt + nY) * lWidth) + (nLastWidth + nX + nCheck) < 0)
										return FALSE;

									pwdDst[((nYCnt + nY) * lWidth) + (nLastWidth + nX + nCheck)] = ((bRedWantedColor << m_stBitsMaskInfo.bRShift) |
										(bGreenWantedColor << m_stBitsMaskInfo.bGShift) |
										(bBlueWantedColor << m_stBitsMaskInfo.bBShift));
								}
								x += (nCurrWidth - rc.left);
							}
							// �� rc.right ��Ϊ��׼���������򱻽�ȡ�������
							else if (nLastWidth <= rc.right && rc.right < nCurrWidth)
							{
								for (INT nCheck = 0; nCheck < rc.right - nLastWidth; nCheck++)
								{
									wPixel = wChooseColor;
									bBlueWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									wPixel = pwSrc[x + nCheck];
									bBlueSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									rBlueRate = (FLOAT)((FLOAT)bBlueSrc / (FLOAT)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));
									rGreenRate = (FLOAT)((FLOAT)bGreenSrc / (FLOAT)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
									bRedRate = (FLOAT)((FLOAT)bRedSrc / (FLOAT)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));

									bBlueWantedColor = (BYTE)(((FLOAT)bBlueWantedColor * rBlueRate));
									bGreenWantedColor = (BYTE)(((FLOAT)bGreenWantedColor * rGreenRate));
									bRedWantedColor = (BYTE)(((FLOAT)bRedWantedColor * bRedRate));

									if (bBlueWantedColor > (m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift))
										bBlueWantedColor = (BYTE)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift);
									if (bGreenWantedColor > (m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift))
										bGreenWantedColor = (BYTE)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift);
									if (bRedWantedColor > (m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift))
										bRedWantedColor = (BYTE)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift);

									BYTE	temp;

									temp = (bsRedColor + (bGreenWantedColor >> 1) + bBlueWantedColor) / 3;

									bBlueWantedColor = temp > 0 ? (temp & bdBlueColor) : 0;
									bGreenWantedColor = temp > 0 ? (temp & (bdGreenColor << 1)) : 0;
									bRedWantedColor = temp > 0 ? (temp & bdRedColor) : 0;

									if (bBlueWantedColor >= 32)  bBlueWantedColor = 31;
									if (bGreenWantedColor >= 64)  bGreenWantedColor = 63;
									if (bRedWantedColor >= 32)  bRedWantedColor = 31;

									pwdDst[((nYCnt + nY) * lWidth) + (nLastWidth + nX + nCheck)] = ((bRedWantedColor << m_stBitsMaskInfo.bRShift) |
										(bGreenWantedColor << m_stBitsMaskInfo.bGShift) |
										(bBlueWantedColor << m_stBitsMaskInfo.bBShift));
								}
								x += nCntCopyWord;
							}
							else
							{
								for (INT nCheck = 0; nCheck < nCntCopyWord; nCheck++)
								{
									wPixel = wChooseColor;
									bBlueWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									wPixel = pwSrc[x + nCheck];
									bBlueSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									rBlueRate = (FLOAT)((FLOAT)bBlueSrc / (FLOAT)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));
									rGreenRate = (FLOAT)((FLOAT)bGreenSrc / (FLOAT)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
									bRedRate = (FLOAT)((FLOAT)bRedSrc / (FLOAT)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));

									bBlueWantedColor = (BYTE)(((FLOAT)bBlueWantedColor * rBlueRate));
									bGreenWantedColor = (BYTE)(((FLOAT)bGreenWantedColor * rGreenRate));
									bRedWantedColor = (BYTE)(((FLOAT)bRedWantedColor * bRedRate));

									if (bBlueWantedColor > (m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift))
										bBlueWantedColor = (BYTE)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift);
									if (bGreenWantedColor > (m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift))
										bGreenWantedColor = (BYTE)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift);
									if (bRedWantedColor > (m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift))
										bRedWantedColor = (BYTE)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift);

									BYTE	temp;

									temp = (bsRedColor + (bGreenWantedColor >> 1) + bBlueWantedColor) / 3;

									bBlueWantedColor = temp > 0 ? (temp & bdBlueColor) : 0;
									bGreenWantedColor = temp > 0 ? (temp & (bdGreenColor << 1)) : 0;
									bRedWantedColor = temp > 0 ? (temp & bdRedColor) : 0;

									if (bBlueWantedColor >= 32)  bBlueWantedColor = 31;
									if (bGreenWantedColor >= 64)  bGreenWantedColor = 63;
									if (bRedWantedColor >= 32)  bRedWantedColor = 31;

									pwdDst[((nYCnt + nY) * lWidth) + (nLastWidth + nX + nCheck)] = ((bRedWantedColor << m_stBitsMaskInfo.bRShift) |
										(bGreenWantedColor << m_stBitsMaskInfo.bGShift) |
										(bBlueWantedColor << m_stBitsMaskInfo.bBShift));
								}
								x += nCntCopyWord;
							}
						}
					}
					else
					{
						// DirectX 9.0 Ǩ�ƣ�ʹ��UnlockRect���Unlock
						m_pddsOffscreenSurface->UnlockRect();
						return FALSE;
					}
				}
				// ���е�ĩβ�ƶ�����һ�еĿ�ͷ��
				nWidthEnd++;

				nWidthStart = nWidthEnd;
				nCurrWidth = 0;
			}
		}

		// DirectX 9.0 Ǩ�ƣ�ʹ��UnlockRect���Unlock
		m_pddsOffscreenSurface->UnlockRect();
		return TRUE;
	}
	return FALSE;
}



BOOL CWHDXGraphicWindow::DrawWithImageForCompClipRgnGray(INT nX, INT nY, INT nXSize, INT nYSize, WORD* pwSrc, WORD wClipWidth, WORD wClipHeight, WORD wChooseColor1, WORD wChooseColor2)
{
	RECT			rc;

	INT	nWidth = nXSize;
	INT	nHeight = nYSize;
	INT	nXOffset = 0;
	INT	nYOffset = 0;
	INT	nStartX = 0;
	INT	nStartY = 0;
	INT	nEndX = wClipWidth - 1;
	INT	nEndY = wClipHeight - 1;

	INT FiveMiddle = 0xf;
	INT SixMiddle = 0x1f;

	BYTE bsBlueColor, bsGreenColor, bsRedColor;
	BYTE bdBlueColor, bdGreenColor, bdRedColor;

	bdBlueColor = 0x0f;
	bdGreenColor = 0x1f;
	bdRedColor = 0x0f;

	if (m_pddsOffscreenSurface != NULL)
	{
		if (nX < nStartX)
		{
			nXOffset = nStartX - nX;
			nWidth = nXSize - nXOffset;
		}
		if ((nX + nXSize - 1) > nEndX)
			nWidth = nEndX - nX - nXOffset + 1;
		if (nY < nStartY)
		{
			nYOffset = nStartY - nY;
			nHeight = nYSize - nYOffset;
		}
		if ((nY + nYSize - 1) > nEndY)
			nHeight = nEndY - nY - nYOffset + 1;

		if ((nWidth > 0) && (nHeight > 0))
		{
			rc.left = nXOffset;
			rc.right = nXOffset + nWidth;
			rc.top = nYOffset;
			rc.bottom = nYOffset + nHeight;

			D3DLOCKED_RECT lockedRect;
			LONG	lWidth;

			if (FAILED(m_pddsOffscreenSurface->LockRect(&lockedRect, NULL, 0)))
			{
				return FALSE;
			}

			WORD* pwdDst = (WORD*)lockedRect.pBits;
			lWidth = lockedRect.Pitch >> 1;

			INT nWidthStart = 0;
			INT nWidthEnd = 0;
			INT nCurrWidth = 0;
			INT nCntCopyWord = 0;
			INT nYCnt = 0;
			INT nLastWidth = 0;

			FLOAT rBlueRate, rGreenRate, bRedRate;
			BYTE  bRedSrc, bGreenSrc, bBlueSrc;
			BYTE  bRedWantedColor, bGreenWantedColor, bBlueWantedColor;
			WORD  wPixel;

			// Y��ü���
			for (nYCnt = 0; nYCnt < rc.top; nYCnt++)
			{
				nWidthEnd += pwSrc[nWidthStart];
				nWidthStart++;
				nWidthEnd++;
				nWidthStart = nWidthEnd;
			}

			// ȷ�� Y ����ʵ��ѭ���� Count��
			for (nYCnt = rc.top; nYCnt < rc.bottom; nYCnt++)
			{
				// ��ȡһ�еĳ��ȣ���λΪ�֣���
				nWidthEnd += pwSrc[nWidthStart];
				nWidthStart++;

				// �����еĳ�������Ļ����ʾ��
				for (INT x = nWidthStart; x < nWidthEnd; )
				{
					if (pwSrc[x] == 0xC0)
					{
						x++;
						nCntCopyWord = pwSrc[x];
						x++;
						nCurrWidth += nCntCopyWord;
					}
					else if (pwSrc[x] == 0xC1)
					{
						x++;
						nCntCopyWord = pwSrc[x];	// ����
						x++;

						nLastWidth = nCurrWidth;
						nCurrWidth += nCntCopyWord;

						if (rc.left > nCurrWidth || rc.right < nLastWidth)
						{
							x += nCntCopyWord;		// Ī����������
						}
						else
						{
							// �� rc.left ��Ϊ��׼���������򱻽�ȡ�������
							if (nLastWidth < rc.left && rc.left <= nCurrWidth)
							{
								x += (rc.left - nLastWidth);

								for (WORD i = 0; i < (sizeof(WORD) * (nCurrWidth - rc.left) / 2); i++)
								{
									bsBlueColor = (BYTE)((pwSrc[x + i] & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bsGreenColor = (BYTE)((pwSrc[x + i] & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bsRedColor = (BYTE)((pwSrc[x + i] & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									// Draw Gray 									
									BYTE	temp;

									temp = (bsRedColor + bsGreenColor / 2 + bsBlueColor) / 3;

									bsBlueColor = temp > 0 ? (temp) : 0;
									bsGreenColor = temp > 0 ? (temp << 1) : 0;
									bsRedColor = temp > 0 ? (temp) : 0;

									if (((nYCnt + nY) * lWidth) + (rc.left + nX) + i < 0)
										return FALSE;

									pwdDst[((nYCnt + nY) * lWidth) + (rc.left + nX) + i] = (bsBlueColor << m_stBitsMaskInfo.bBShift) | (bsGreenColor << m_stBitsMaskInfo.bGShift) | (bsRedColor << m_stBitsMaskInfo.bRShift);
								}

								x += (nCurrWidth - rc.left);
							}
							// �� rc.right ��Ϊ��׼���������򱻽�ȡ�������
							else if (nLastWidth <= rc.right && rc.right < nCurrWidth)
							{
								for (WORD i = 0; i < (sizeof(WORD) * (rc.right - nLastWidth)); i++)
								{
									bsBlueColor = (BYTE)((pwSrc[x + i] & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bsGreenColor = (BYTE)((pwSrc[x + i] & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bsRedColor = (BYTE)((pwSrc[x + i] & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									// Draw Gray 									

									BYTE	temp;

									temp = (bsRedColor + bsGreenColor / 2 + bsBlueColor) / 3;

									bsBlueColor = temp > 0 ? (temp) : 0;
									bsGreenColor = temp > 0 ? (temp << 1) : 0;
									bsRedColor = temp > 0 ? (temp) : 0;

									pwdDst[((nYCnt + nY) * lWidth) + (nLastWidth + nX) + i] = (bsBlueColor << m_stBitsMaskInfo.bBShift) | (bsGreenColor << m_stBitsMaskInfo.bGShift) | (bsRedColor << m_stBitsMaskInfo.bRShift);
								}
								//								memcpy(&pwdDst[((nYCnt+nY) * lWidth) + (nLastWidth+nX)], &pwSrc[x], sizeof(WORD)*(rc.right-nLastWidth));
								x += nCntCopyWord;
							}
							else
							{
								for (WORD i = 0; i < (sizeof(WORD) * nCntCopyWord / 2); i++)
								{
									bsBlueColor = (BYTE)((pwSrc[x + i] & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bsGreenColor = (BYTE)((pwSrc[x + i] & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bsRedColor = (BYTE)((pwSrc[x + i] & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);
									// Draw Gray 
									BYTE	temp;

									temp = (bsRedColor + bsGreenColor / 2 + bsBlueColor) / 3;

									bsBlueColor = temp > 0 ? (temp) : 0;
									bsGreenColor = temp > 0 ? (temp << 1) : 0;
									bsRedColor = temp > 0 ? (temp) : 0;

									pwdDst[((nYCnt + nY) * lWidth) + (nLastWidth + nX) + i] = (bsBlueColor << m_stBitsMaskInfo.bBShift) | (bsGreenColor << m_stBitsMaskInfo.bGShift) | (bsRedColor << m_stBitsMaskInfo.bRShift);
								}
								x += nCntCopyWord;
							}
						}
					}
					else if (pwSrc[x] == 0xC2 || pwSrc[x] == 0xC3)
					{
						WORD wDyingKind, wChooseColor;
						wDyingKind = pwSrc[x];
						switch (wDyingKind)
						{
						case 0xC2:
							wChooseColor = wChooseColor1;
							break;
						case 0xC3:
							wChooseColor = wChooseColor2;
							break;
						}
						x++;
						nCntCopyWord = pwSrc[x];
						x++;

						nLastWidth = nCurrWidth;
						nCurrWidth += nCntCopyWord;

						if (rc.left > nCurrWidth || rc.right < nLastWidth)
						{
							x += nCntCopyWord;
						}
						else
						{
							// �� rc.left ��Ϊ��׼���������򱻽�ȡ�������
							if (nLastWidth < rc.left && rc.left <= nCurrWidth)
							{
								x += (rc.left - nLastWidth);
								for (INT nCheck = 0; nCheck < nCurrWidth - rc.left; nCheck++)
								{
									wPixel = wChooseColor;
									bBlueWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									wPixel = pwSrc[x + nCheck];
									bBlueSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									rBlueRate = (FLOAT)((FLOAT)bBlueSrc / (FLOAT)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));
									rGreenRate = (FLOAT)((FLOAT)bGreenSrc / (FLOAT)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
									bRedRate = (FLOAT)((FLOAT)bRedSrc / (FLOAT)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));

									bBlueWantedColor = (BYTE)(((FLOAT)bBlueWantedColor * rBlueRate));
									bGreenWantedColor = (BYTE)(((FLOAT)bGreenWantedColor * rGreenRate));
									bRedWantedColor = (BYTE)(((FLOAT)bRedWantedColor * bRedRate));

									if (bBlueWantedColor > (m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift))
										bBlueWantedColor = (BYTE)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift);
									if (bGreenWantedColor > (m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift))
										bGreenWantedColor = (BYTE)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift);
									if (bRedWantedColor > (m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift))
										bRedWantedColor = (BYTE)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift);

									// Draw Gray 
									BYTE	temp;
									temp = (bRedWantedColor + bGreenWantedColor / 2 + bBlueWantedColor) / 3;

									bBlueWantedColor = temp > 0 ? (temp) : 0;
									bGreenWantedColor = temp > 0 ? (temp << 1) : 0;
									bRedWantedColor = temp > 0 ? (temp) : 0;

									if (((nYCnt + nY) * lWidth) + (nLastWidth + nX + nCheck) < 0)
										return FALSE;

									pwdDst[((nYCnt + nY) * lWidth) + (nLastWidth + nX + nCheck)] = ((bRedWantedColor << m_stBitsMaskInfo.bRShift) |
										(bGreenWantedColor << m_stBitsMaskInfo.bGShift) |
										(bBlueWantedColor << m_stBitsMaskInfo.bBShift));
								}
								x += (nCurrWidth - rc.left);
							}
							// �� rc.right ��Ϊ��׼���������򱻽�ȡ�������
							else if (nLastWidth <= rc.right && rc.right < nCurrWidth)
							{
								for (INT nCheck = 0; nCheck < rc.right - nLastWidth; nCheck++)
								{
									wPixel = wChooseColor;
									bBlueWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									wPixel = pwSrc[x + nCheck];
									bBlueSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									rBlueRate = (FLOAT)((FLOAT)bBlueSrc / (FLOAT)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));
									rGreenRate = (FLOAT)((FLOAT)bGreenSrc / (FLOAT)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
									bRedRate = (FLOAT)((FLOAT)bRedSrc / (FLOAT)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));

									bBlueWantedColor = (BYTE)(((FLOAT)bBlueWantedColor * rBlueRate));
									bGreenWantedColor = (BYTE)(((FLOAT)bGreenWantedColor * rGreenRate));
									bRedWantedColor = (BYTE)(((FLOAT)bRedWantedColor * bRedRate));

									if (bBlueWantedColor > (m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift))
										bBlueWantedColor = (BYTE)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift);
									if (bGreenWantedColor > (m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift))
										bGreenWantedColor = (BYTE)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift);
									if (bRedWantedColor > (m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift))
										bRedWantedColor = (BYTE)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift);

									// Draw Gray 
									BYTE	temp;
									temp = (bRedWantedColor + bGreenWantedColor / 2 + bBlueWantedColor) / 3;

									bBlueWantedColor = temp > 0 ? (temp) : 0;
									bGreenWantedColor = temp > 0 ? (temp << 1) : 0;
									bRedWantedColor = temp > 0 ? (temp) : 0;

									pwdDst[((nYCnt + nY) * lWidth) + (nLastWidth + nX + nCheck)] = ((bRedWantedColor << m_stBitsMaskInfo.bRShift) |
										(bGreenWantedColor << m_stBitsMaskInfo.bGShift) |
										(bBlueWantedColor << m_stBitsMaskInfo.bBShift));
								}
								x += nCntCopyWord;
							}
							else
							{
								for (INT nCheck = 0; nCheck < nCntCopyWord; nCheck++)
								{
									wPixel = wChooseColor;
									bBlueWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									wPixel = pwSrc[x + nCheck];
									bBlueSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
									bGreenSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
									bRedSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

									rBlueRate = (FLOAT)((FLOAT)bBlueSrc / (FLOAT)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));
									rGreenRate = (FLOAT)((FLOAT)bGreenSrc / (FLOAT)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
									bRedRate = (FLOAT)((FLOAT)bRedSrc / (FLOAT)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));

									bBlueWantedColor = (BYTE)(((FLOAT)bBlueWantedColor * rBlueRate));
									bGreenWantedColor = (BYTE)(((FLOAT)bGreenWantedColor * rGreenRate));
									bRedWantedColor = (BYTE)(((FLOAT)bRedWantedColor * bRedRate));

									if (bBlueWantedColor > (m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift))
										bBlueWantedColor = (BYTE)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift);
									if (bGreenWantedColor > (m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift))
										bGreenWantedColor = (BYTE)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift);
									if (bRedWantedColor > (m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift))
										bRedWantedColor = (BYTE)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift);

									// Draw Gray 
									BYTE	temp;
									temp = (bRedWantedColor + bGreenWantedColor / 2 + bBlueWantedColor) / 3;

									bBlueWantedColor = temp > 0 ? (temp) : 0;
									bGreenWantedColor = temp > 0 ? (temp << 1) : 0;
									bRedWantedColor = temp > 0 ? (temp) : 0;

									pwdDst[((nYCnt + nY) * lWidth) + (nLastWidth + nX + nCheck)] = ((bRedWantedColor << m_stBitsMaskInfo.bRShift) |
										(bGreenWantedColor << m_stBitsMaskInfo.bGShift) |
										(bBlueWantedColor << m_stBitsMaskInfo.bBShift));
								}
								x += nCntCopyWord;
							}
						}
					}
					else
					{
						// DirectX 9.0 Ǩ�ƣ�ʹ��UnlockRect���Unlock
						m_pddsOffscreenSurface->UnlockRect();
						return FALSE;
					}
				}
				// ���е�ĩβ�ƶ�����һ�еĿ�ͷ��
				nWidthEnd++;

				nWidthStart = nWidthEnd;
				nCurrWidth = 0;
			}
		}

		// DirectX 9.0 Ǩ�ƣ�ʹ��UnlockRect���Unlock
		m_pddsOffscreenSurface->UnlockRect();
		return TRUE;
	}
	return FALSE;
}


VOID CWHDXGraphicWindow::DrawWithGrayBackBuffer()
{
	D3DLOCKED_RECT	lockedRect;
	RECT			rc = { 0, 0, 800, 500 };

	if (m_pddsOffscreenSurface == NULL)			return;

	LONG	lWidth;

	// DirectX 9.0 Ǩ�ƣ�ʹ��LockRect���Lock
	if (FAILED(m_pddsOffscreenSurface->LockRect(&lockedRect, NULL, 0)))
	{
		return;
	}

	lWidth = lockedRect.Pitch >> 1;  // DirectX 9.0 Ǩ�ƣ�ʹ��Pitch���lPitch

	WORD* pwdSrc, * pwdDst;
	BYTE bsBlueColor, bsGreenColor, bsRedColor;
	pwdDst = (WORD*)lockedRect.pBits;   // DirectX 9.0 Ǩ�ƣ�ʹ��pBits���lpSurface
	pwdSrc = (WORD*)lockedRect.pBits;

	for (INT y = rc.top; y < rc.bottom; y++)
	{
		for (INT x = rc.left; x < rc.right; x++)
		{
			bsBlueColor = (BYTE)((pwdSrc[y * lWidth + x] & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
			bsGreenColor = (BYTE)((pwdSrc[y * lWidth + x] & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
			bsRedColor = (BYTE)((pwdSrc[y * lWidth + x] & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

			// Draw Gray
			BYTE	temp;

			temp = (bsRedColor + bsGreenColor / 2 + bsBlueColor) / 3;

			bsBlueColor = temp > 0 ? (temp) : 0;
			bsGreenColor = temp > 0 ? (temp << 1) : 0;
			bsRedColor = temp > 0 ? (temp) : 0;

			pwdDst[y * lWidth + x] = (bsBlueColor << m_stBitsMaskInfo.bBShift) | (bsGreenColor << m_stBitsMaskInfo.bGShift) | (bsRedColor << m_stBitsMaskInfo.bRShift);
		}
	}

	// DirectX 9.0 Ǩ�ƣ�ʹ��UnlockRect���Unlock
	m_pddsOffscreenSurface->UnlockRect();
}



/******************************************************************************************************************

	������ : CWHDXGraphicWindow::DrawWithImageForCompClipRgnToMem()

	���� :
	�������� :

	Ŀ�� : ��ѹ����Դͼ���ѹ��ָ����С�������Ƶ��ṩ���ڴ�(pwDst)�С�

	���� : INT nStartX
			 INT nStartY
			 INT nWantedXSize
			 INT nWantedYSize
			 WORD* pwSrc
			 WORD wClipWidth
			 WORD wClipHeight
			 WORD wChooseColor1
			 WORD wChooseColor2
	��� : BOOL

	[����][�޸���] : �޸�����

*******************************************************************************************************************/
BOOL CWHDXGraphicWindow::DrawWithImageForCompColorToMem(RECT rcWanted, WORD* pwSrc, WORD wColor, WORD* pwDst)
{
	if (rcWanted.left < 0 || rcWanted.right < 0 || rcWanted.top < 0 || rcWanted.bottom < 0 ||
		rcWanted.right - rcWanted.left < 0 || rcWanted.bottom - rcWanted.top < 0 || pwDst == NULL)
		return FALSE;

	INT nWidthStart = 0;
	INT nWidthEnd = 0;
	INT nCurrWidth = 0;
	INT nCntCopyWord = 0;
	INT nYCnt = 0;
	INT nLastWidth = 0;

	// Y��ü���
	for (nYCnt = 0; nYCnt < rcWanted.top; nYCnt++)
	{
		nWidthEnd += pwSrc[nWidthStart];
		nWidthStart++;
		nWidthEnd++;
		nWidthStart = nWidthEnd;
	}

	INT nNewCurrWidth = 0;

	FLOAT rBlueRate, rGreenRate, rRedRate;
	BYTE  bRedSrc, bGreenSrc, bBlueSrc;
	BYTE  bRedStateColor, bGreenStateColor, bBlueStateColor;
	WORD  wPixel;

	// ȷ�� Y ����ʵ��ѭ���� Count��
	for (nYCnt = rcWanted.top; nYCnt < rcWanted.bottom; nYCnt++)
	{
		// ��ȡһ�еĳ��ȣ���λΪ�֣���
		nWidthEnd += pwSrc[nWidthStart];
		nWidthStart++;

		// �����еĳ�������Ļ����ʾ��
		for (INT x = nWidthStart; x < nWidthEnd; )
		{
			if (pwSrc[x] == 0xC0)
			{
				x++;
				nCntCopyWord = pwSrc[x];
				x++;
				nLastWidth = nCurrWidth;
				nCurrWidth += nCntCopyWord;

				// �� rcWanted.left ��Ϊ��׼���������򱻽�ȡ�������
				if (nLastWidth <= rcWanted.left && nCurrWidth >= rcWanted.right)
				{
					if (((nYCnt - rcWanted.top) * (rcWanted.right - rcWanted.left)) + nNewCurrWidth < 0)
						return FALSE;

					memset(&pwDst[((nYCnt - rcWanted.top) * (rcWanted.right - rcWanted.left)) + nNewCurrWidth], 0, sizeof(WORD) * (rcWanted.right - rcWanted.left));
					nNewCurrWidth += rcWanted.right - rcWanted.left;
				}
				else if (nLastWidth < rcWanted.left && nCurrWidth > rcWanted.left)
				{
					memset(&pwDst[((nYCnt - rcWanted.top) * (rcWanted.right - rcWanted.left)) + nNewCurrWidth], 0, sizeof(WORD) * (nCurrWidth - rcWanted.left));
					nNewCurrWidth += nCurrWidth - rcWanted.left;
				}
				else if (nLastWidth >= rcWanted.left && nCurrWidth <= rcWanted.right)
				{
					memset(&pwDst[((nYCnt - rcWanted.top) * (rcWanted.right - rcWanted.left)) + nNewCurrWidth], 0, sizeof(WORD) * (nCntCopyWord));
					nNewCurrWidth += nCntCopyWord;
				}
				// �� rcWanted.right ��Ϊ��׼���������򱻽�ȡ�������
				else if (nLastWidth < rcWanted.right && nCurrWidth > rcWanted.right)
				{
					memset(&pwDst[((nYCnt - rcWanted.top) * (rcWanted.right - rcWanted.left)) + nNewCurrWidth], 0, sizeof(WORD) * (rcWanted.right - nLastWidth));
					nNewCurrWidth += rcWanted.right - nLastWidth;
				}
			}
			else if (pwSrc[x] == 0xC1 || pwSrc[x] == 0xC2 || pwSrc[x] == 0xC3)
			{
				x++;
				nCntCopyWord = pwSrc[x];
				x++;

				nLastWidth = nCurrWidth;
				nCurrWidth += nCntCopyWord;

				if (rcWanted.left > nCurrWidth || rcWanted.right < nLastWidth)
				{
					x += nCntCopyWord;
				}
				else
				{
					// �� rcWanted.left ��Ϊ��׼���������򱻽�ȡ�������
					if (nLastWidth <= rcWanted.left && nCurrWidth >= rcWanted.right)
					{
						x += (rcWanted.left - nLastWidth);
						for (INT nCheck = 0; nCheck < rcWanted.right - rcWanted.left; nCheck++)
						{
							wPixel = wColor;
							bBlueStateColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
							bGreenStateColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
							bRedStateColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

							wPixel = pwSrc[x + nCheck];
							bBlueSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
							bGreenSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
							bRedSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

							if (!wColor)
							{
								BYTE bTemp = (BYTE)(bBlueSrc + (bGreenSrc >> 1) + bRedSrc) / 3;
								if (bTemp > 31)	bTemp = 31;
								bBlueStateColor = bRedStateColor = bTemp;
								bGreenStateColor = bTemp << 1;
							}
							else
							{
								rBlueRate = (FLOAT)((FLOAT)bBlueSrc / (FLOAT)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));
								rGreenRate = (FLOAT)((FLOAT)bGreenSrc / (FLOAT)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
								rRedRate = (FLOAT)((FLOAT)bRedSrc / (FLOAT)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));

								bBlueStateColor = (BYTE)((FLOAT)bBlueStateColor * rBlueRate);
								bGreenStateColor = (BYTE)((FLOAT)bGreenStateColor * rGreenRate);
								bRedStateColor = (BYTE)((FLOAT)bRedStateColor * rRedRate);

								if (bBlueStateColor > (m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift))
									bBlueStateColor = (BYTE)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift);
								if (bGreenStateColor > (m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift))
									bGreenStateColor = (BYTE)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift);
								if (bRedStateColor > (m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift))
									bRedStateColor = (BYTE)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift);
							}

							if (!bRedStateColor && !bGreenStateColor && !bBlueStateColor)		bRedStateColor = bGreenStateColor = bBlueStateColor = 1;

							if (((nYCnt - rcWanted.top) * (rcWanted.right - rcWanted.left)) + (nNewCurrWidth + nCheck) < 0)
								return FALSE;

							pwDst[((nYCnt - rcWanted.top) * (rcWanted.right - rcWanted.left)) + (nNewCurrWidth + nCheck)] = ((bRedStateColor << m_stBitsMaskInfo.bRShift) |
								(bGreenStateColor << m_stBitsMaskInfo.bGShift) |
								(bBlueStateColor << m_stBitsMaskInfo.bBShift));
						}
						nNewCurrWidth += rcWanted.right - rcWanted.left;
						x += (nCurrWidth - rcWanted.left);
					}
					else if (nLastWidth < rcWanted.left && nCurrWidth > rcWanted.left)
					{
						x += (rcWanted.left - nLastWidth);
						for (INT nCheck = 0; nCheck < nCurrWidth - rcWanted.left; nCheck++)
						{
							wPixel = wColor;
							bBlueStateColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
							bGreenStateColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
							bRedStateColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

							wPixel = pwSrc[x + nCheck];
							bBlueSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
							bGreenSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
							bRedSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

							if (!wColor)
							{
								BYTE bTemp = (BYTE)(bBlueSrc + (bGreenSrc >> 1) + bRedSrc) / 3;
								if (bTemp > 31)	bTemp = 31;
								bBlueStateColor = bRedStateColor = bTemp;
								bGreenStateColor = bTemp << 1;
							}
							else
							{
								rBlueRate = (FLOAT)((FLOAT)bBlueSrc / (FLOAT)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));
								rGreenRate = (FLOAT)((FLOAT)bGreenSrc / (FLOAT)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
								rRedRate = (FLOAT)((FLOAT)bRedSrc / (FLOAT)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));

								bBlueStateColor = (BYTE)((FLOAT)bBlueStateColor * rBlueRate);
								bGreenStateColor = (BYTE)((FLOAT)bGreenStateColor * rGreenRate);
								bRedStateColor = (BYTE)((FLOAT)bRedStateColor * rRedRate);

								if (bBlueStateColor > (m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift))
									bBlueStateColor = (BYTE)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift);
								if (bGreenStateColor > (m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift))
									bGreenStateColor = (BYTE)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift);
								if (bRedStateColor > (m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift))
									bRedStateColor = (BYTE)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift);
							}

							if (!bRedStateColor && !bGreenStateColor && !bBlueStateColor)		bRedStateColor = bGreenStateColor = bBlueStateColor = 1;

							pwDst[((nYCnt - rcWanted.top) * (rcWanted.right - rcWanted.left)) + (nNewCurrWidth + nCheck)] = ((bRedStateColor << m_stBitsMaskInfo.bRShift) |
								(bGreenStateColor << m_stBitsMaskInfo.bGShift) |
								(bBlueStateColor << m_stBitsMaskInfo.bBShift));
						}
						nNewCurrWidth += nCurrWidth - rcWanted.left;
						x += (nCurrWidth - rcWanted.left);
					}
					else if (nLastWidth >= rcWanted.left && nCurrWidth <= rcWanted.right)
					{
						for (INT nCheck = 0; nCheck < nCntCopyWord; nCheck++)
						{
							wPixel = wColor;
							bBlueStateColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
							bGreenStateColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
							bRedStateColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

							wPixel = pwSrc[x + nCheck];
							bBlueSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
							bGreenSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
							bRedSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

							if (!wColor)
							{
								BYTE bTemp = (BYTE)(bBlueSrc + (bGreenSrc >> 1) + bRedSrc) / 3;
								if (bTemp > 31)	bTemp = 31;
								bBlueStateColor = bRedStateColor = bTemp;
								bGreenStateColor = bTemp << 1;
							}
							else
							{
								if (nCheck == 17)
									int b = 0;

								rBlueRate = (FLOAT)((FLOAT)bBlueSrc / (FLOAT)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));
								rGreenRate = (FLOAT)((FLOAT)bGreenSrc / (FLOAT)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
								rRedRate = (FLOAT)((FLOAT)bRedSrc / (FLOAT)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));

								bBlueStateColor = (BYTE)((FLOAT)bBlueStateColor * rBlueRate);
								bGreenStateColor = (BYTE)((FLOAT)bGreenStateColor * rGreenRate);
								bRedStateColor = (BYTE)((FLOAT)bRedStateColor * rRedRate);

								if (bBlueStateColor > (m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift))
									bBlueStateColor = (BYTE)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift);
								if (bGreenStateColor > (m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift))
									bGreenStateColor = (BYTE)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift);
								if (bRedStateColor > (m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift))
									bRedStateColor = (BYTE)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift);
							}

							if (!bRedStateColor && !bGreenStateColor && !bBlueStateColor)		bRedStateColor = bGreenStateColor = bBlueStateColor = 1;

							pwDst[((nYCnt - rcWanted.top) * (rcWanted.right - rcWanted.left)) + (nNewCurrWidth + nCheck)] = ((bRedStateColor << m_stBitsMaskInfo.bRShift) |
								(bGreenStateColor << m_stBitsMaskInfo.bGShift) |
								(bBlueStateColor << m_stBitsMaskInfo.bBShift));
						}
						nNewCurrWidth += nCntCopyWord;
						x += nCntCopyWord;
					}
					// �� rcWanted.right ��Ϊ��׼���������򱻽�ȡ�������
					else if (nLastWidth < rcWanted.right && nCurrWidth > rcWanted.right)
					{
						for (INT nCheck = 0; nCheck < rcWanted.right - nLastWidth; nCheck++)
						{
							wPixel = wColor;
							bBlueStateColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
							bGreenStateColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
							bRedStateColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

							wPixel = pwSrc[x + nCheck];
							bBlueSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
							bGreenSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
							bRedSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

							if (!wColor)
							{
								BYTE bTemp = (BYTE)(bBlueSrc + (bGreenSrc >> 1) + bRedSrc) / 3;
								if (bTemp > 31)	bTemp = 31;
								bBlueStateColor = bRedStateColor = bTemp;
								bGreenStateColor = bTemp << 1;
							}
							else
							{
								rBlueRate = (FLOAT)((FLOAT)bBlueSrc / (FLOAT)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));
								rGreenRate = (FLOAT)((FLOAT)bGreenSrc / (FLOAT)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
								rRedRate = (FLOAT)((FLOAT)bRedSrc / (FLOAT)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));

								bBlueStateColor = (BYTE)((FLOAT)bBlueStateColor * rBlueRate);
								bGreenStateColor = (BYTE)((FLOAT)bGreenStateColor * rGreenRate);
								bRedStateColor = (BYTE)((FLOAT)bRedStateColor * rRedRate);

								if (bBlueStateColor > (m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift))
									bBlueStateColor = (BYTE)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift);
								if (bGreenStateColor > (m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift))
									bGreenStateColor = (BYTE)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift);
								if (bRedStateColor > (m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift))
									bRedStateColor = (BYTE)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift);
							}

							if (!bRedStateColor && !bGreenStateColor && !bBlueStateColor)		bRedStateColor = bGreenStateColor = bBlueStateColor = 1;

							pwDst[((nYCnt - rcWanted.top) * (rcWanted.right - rcWanted.left)) + (nNewCurrWidth + nCheck)] = ((bRedStateColor << m_stBitsMaskInfo.bRShift) |
								(bGreenStateColor << m_stBitsMaskInfo.bGShift) |
								(bBlueStateColor << m_stBitsMaskInfo.bBShift));
						}
						nNewCurrWidth += rcWanted.right - nLastWidth;
						x += nCntCopyWord;
					}
					else
					{
						x += nCntCopyWord;
					}
				}
			}
			else
			{
				return FALSE;
			}
		}
		// ���е�ĩβ�ƶ�����һ�еĿ�ͷ��
		nWidthEnd++;

		nWidthStart = nWidthEnd;
		nCurrWidth = 0;
		nNewCurrWidth = 0;
	}
	return TRUE;
}




BOOL CWHDXGraphicWindow::DrawWithImageForCompToMem(RECT rcWanted, WORD* pwSrc, WORD wChooseColor1, WORD wChooseColor2, WORD* pwDst)
{
	if (rcWanted.left < 0 || rcWanted.right < 0 || rcWanted.top < 0 || rcWanted.bottom < 0 ||
		rcWanted.right - rcWanted.left < 0 || rcWanted.bottom - rcWanted.top < 0 || pwDst == NULL)
		return FALSE;

	INT nWidthStart = 0;
	INT nWidthEnd = 0;
	INT nCurrWidth = 0;
	INT nCntCopyWord = 0;
	INT nYCnt = 0;
	INT nLastWidth = 0;

	// Y��ü���
	for (nYCnt = 0; nYCnt < rcWanted.top; nYCnt++)
	{
		nWidthEnd += pwSrc[nWidthStart];
		nWidthStart++;
		nWidthEnd++;
		nWidthStart = nWidthEnd;
	}

	INT nNewCurrWidth = 0;

	FLOAT rBlueRate, rGreenRate, bRedRate;
	BYTE  bRedSrc, bGreenSrc, bBlueSrc;
	BYTE  bRedWantedColor, bGreenWantedColor, bBlueWantedColor;
	WORD  wPixel;

	// ȷ�� Y ����ʵ��ѭ���� Count��
	for (nYCnt = rcWanted.top; nYCnt < rcWanted.bottom; nYCnt++)
	{
		// ��ȡһ�еĳ��ȣ���λΪ�֣���
		nWidthEnd += pwSrc[nWidthStart];
		nWidthStart++;

		// �����еĳ�������Ļ����ʾ��
		for (INT x = nWidthStart; x < nWidthEnd; )
		{
			if (pwSrc[x] == 0xC0)
			{
				x++;
				nCntCopyWord = pwSrc[x];
				x++;
				nLastWidth = nCurrWidth;
				nCurrWidth += nCntCopyWord;

				// �� rcWanted.left ��Ϊ��׼���������򱻽�ȡ�������
				if (nLastWidth <= rcWanted.left && nCurrWidth >= rcWanted.right)
				{
					if (((nYCnt - rcWanted.top) * (rcWanted.right - rcWanted.left)) + nNewCurrWidth < 0)
						return FALSE;

					memset(&pwDst[((nYCnt - rcWanted.top) * (rcWanted.right - rcWanted.left)) + nNewCurrWidth], 0, sizeof(WORD) * (rcWanted.right - rcWanted.left));
					nNewCurrWidth += rcWanted.right - rcWanted.left;
				}
				else if (nLastWidth < rcWanted.left && nCurrWidth > rcWanted.left)
				{
					memset(&pwDst[((nYCnt - rcWanted.top) * (rcWanted.right - rcWanted.left)) + nNewCurrWidth], 0, sizeof(WORD) * (nCurrWidth - rcWanted.left));
					nNewCurrWidth += nCurrWidth - rcWanted.left;
				}
				else if (nLastWidth >= rcWanted.left && nCurrWidth <= rcWanted.right)
				{
					memset(&pwDst[((nYCnt - rcWanted.top) * (rcWanted.right - rcWanted.left)) + nNewCurrWidth], 0, sizeof(WORD) * (nCntCopyWord));
					nNewCurrWidth += nCntCopyWord;
				}
				// �� rcWanted.right ��Ϊ��׼���������򱻽�ȡ�������
				else if (nLastWidth < rcWanted.right && nCurrWidth > rcWanted.right)
				{
					memset(&pwDst[((nYCnt - rcWanted.top) * (rcWanted.right - rcWanted.left)) + nNewCurrWidth], 0, sizeof(WORD) * (rcWanted.right - nLastWidth));
					nNewCurrWidth += rcWanted.right - nLastWidth;
				}
			}
			else if (pwSrc[x] == 0xC1)
			{
				x++;
				nCntCopyWord = pwSrc[x];
				x++;

				nLastWidth = nCurrWidth;
				nCurrWidth += nCntCopyWord;

				if (rcWanted.left > nCurrWidth || rcWanted.right < nLastWidth)
				{
					x += nCntCopyWord;
				}
				else
				{
					// �� rcWanted.left ��Ϊ��׼���������򱻽�ȡ�������
					if (nLastWidth <= rcWanted.left && nCurrWidth >= rcWanted.right)
					{
						if (((nYCnt - rcWanted.top) * (rcWanted.right - rcWanted.left)) + nNewCurrWidth < 0)
							return FALSE;

						x += (rcWanted.left - nLastWidth);
						memcpy(&pwDst[((nYCnt - rcWanted.top) * (rcWanted.right - rcWanted.left)) + nNewCurrWidth], &pwSrc[x], sizeof(WORD) * (rcWanted.right - rcWanted.left));
						nNewCurrWidth += rcWanted.right - rcWanted.left;
						x += (nCurrWidth - rcWanted.left);
					}
					else if (nLastWidth < rcWanted.left && nCurrWidth > rcWanted.left)
					{
						x += (rcWanted.left - nLastWidth);
						memcpy(&pwDst[((nYCnt - rcWanted.top) * (rcWanted.right - rcWanted.left)) + nNewCurrWidth], &pwSrc[x], sizeof(WORD) * (nCurrWidth - rcWanted.left));
						nNewCurrWidth += nCurrWidth - rcWanted.left;
						x += (nCurrWidth - rcWanted.left);
					}
					else if (nLastWidth >= rcWanted.left && nCurrWidth <= rcWanted.right)
					{
						memcpy(&pwDst[((nYCnt - rcWanted.top) * (rcWanted.right - rcWanted.left)) + nNewCurrWidth], &pwSrc[x], sizeof(WORD) * nCntCopyWord);
						nNewCurrWidth += nCntCopyWord;
						x += nCntCopyWord;
					}
					// �� rcWanted.right ��Ϊ��׼���������򱻽�ȡ�������
					else if (nLastWidth < rcWanted.right && nCurrWidth > rcWanted.right)
					{
						memcpy(&pwDst[((nYCnt - rcWanted.top) * (rcWanted.right - rcWanted.left)) + nNewCurrWidth], &pwSrc[x], sizeof(WORD) * (rcWanted.right - nLastWidth));
						nNewCurrWidth += rcWanted.right - nLastWidth;
						x += nCntCopyWord;
					}
					else
					{
						x += nCntCopyWord;
					}
				}
			}
			else if (pwSrc[x] == 0xC2 || pwSrc[x] == 0xC3)
			{
				WORD wDyingKind, wChooseColor;
				wDyingKind = pwSrc[x];
				switch (wDyingKind)
				{
				case 0xC2:
					wChooseColor = wChooseColor1;
					break;
				case 0xC3:
					wChooseColor = wChooseColor2;
					break;
				}
				x++;
				nCntCopyWord = pwSrc[x];
				x++;

				nLastWidth = nCurrWidth;
				nCurrWidth += nCntCopyWord;

				if (rcWanted.left > nCurrWidth || rcWanted.right < nLastWidth)
				{
					x += nCntCopyWord;
				}
				else
				{
					// �� rcWanted.left ��Ϊ��׼���������򱻽�ȡ�������
					if (nLastWidth <= rcWanted.left && nCurrWidth >= rcWanted.right)
					{
						x += (rcWanted.left - nLastWidth);
						for (INT nCheck = 0; nCheck < rcWanted.right - rcWanted.left; nCheck++)
						{
							wPixel = wChooseColor;
							bBlueWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
							bGreenWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
							bRedWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

							wPixel = pwSrc[x + nCheck];
							bBlueSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
							bGreenSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
							bRedSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

							rBlueRate = (FLOAT)((FLOAT)bBlueSrc / (FLOAT)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));
							rGreenRate = (FLOAT)((FLOAT)bGreenSrc / (FLOAT)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
							bRedRate = (FLOAT)((FLOAT)bRedSrc / (FLOAT)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));

							bBlueWantedColor = (BYTE)(((FLOAT)bBlueWantedColor * rBlueRate));
							bGreenWantedColor = (BYTE)(((FLOAT)bGreenWantedColor * rGreenRate));
							bRedWantedColor = (BYTE)(((FLOAT)bRedWantedColor * bRedRate));

							if (bBlueWantedColor > (m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift))
								bBlueWantedColor = (BYTE)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift);
							if (bGreenWantedColor > (m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift))
								bGreenWantedColor = (BYTE)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift);
							if (bRedWantedColor > (m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift))
								bRedWantedColor = (BYTE)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift);

							if (((nYCnt - rcWanted.top) * (rcWanted.right - rcWanted.left)) + (nNewCurrWidth + nCheck) < 0)
								return FALSE;

							pwDst[((nYCnt - rcWanted.top) * (rcWanted.right - rcWanted.left)) + (nNewCurrWidth + nCheck)] = ((bRedWantedColor << m_stBitsMaskInfo.bRShift) |
								(bGreenWantedColor << m_stBitsMaskInfo.bGShift) |
								(bBlueWantedColor << m_stBitsMaskInfo.bBShift));
						}
						nNewCurrWidth += rcWanted.right - rcWanted.left;
						x += (nCurrWidth - rcWanted.left);
					}
					else if (nLastWidth < rcWanted.left && nCurrWidth > rcWanted.left)
					{
						x += (rcWanted.left - nLastWidth);
						for (INT nCheck = 0; nCheck < nCurrWidth - rcWanted.left; nCheck++)
						{
							wPixel = wChooseColor;
							bBlueWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
							bGreenWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
							bRedWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

							wPixel = pwSrc[x + nCheck];
							bBlueSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
							bGreenSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
							bRedSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

							rBlueRate = (FLOAT)((FLOAT)bBlueSrc / (FLOAT)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));
							rGreenRate = (FLOAT)((FLOAT)bGreenSrc / (FLOAT)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
							bRedRate = (FLOAT)((FLOAT)bRedSrc / (FLOAT)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));

							bBlueWantedColor = (BYTE)(((FLOAT)bBlueWantedColor * rBlueRate));
							bGreenWantedColor = (BYTE)(((FLOAT)bGreenWantedColor * rGreenRate));
							bRedWantedColor = (BYTE)(((FLOAT)bRedWantedColor * bRedRate));

							if (bBlueWantedColor > (m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift))
								bBlueWantedColor = (BYTE)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift);
							if (bGreenWantedColor > (m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift))
								bGreenWantedColor = (BYTE)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift);
							if (bRedWantedColor > (m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift))
								bRedWantedColor = (BYTE)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift);

							pwDst[((nYCnt - rcWanted.top) * (rcWanted.right - rcWanted.left)) + (nNewCurrWidth + nCheck)] = ((bRedWantedColor << m_stBitsMaskInfo.bRShift) |
								(bGreenWantedColor << m_stBitsMaskInfo.bGShift) |
								(bBlueWantedColor << m_stBitsMaskInfo.bBShift));
						}
						nNewCurrWidth += nCurrWidth - rcWanted.left;
						x += (nCurrWidth - rcWanted.left);
					}
					else if (nLastWidth >= rcWanted.left && nCurrWidth <= rcWanted.right)
					{
						for (INT nCheck = 0; nCheck < nCntCopyWord; nCheck++)
						{
							wPixel = wChooseColor;
							bBlueWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
							bGreenWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
							bRedWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

							wPixel = pwSrc[x + nCheck];
							bBlueSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
							bGreenSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
							bRedSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

							rBlueRate = (FLOAT)((FLOAT)bBlueSrc / (FLOAT)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));
							rGreenRate = (FLOAT)((FLOAT)bGreenSrc / (FLOAT)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
							bRedRate = (FLOAT)((FLOAT)bRedSrc / (FLOAT)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));

							bBlueWantedColor = (BYTE)(((FLOAT)bBlueWantedColor * rBlueRate));
							bGreenWantedColor = (BYTE)(((FLOAT)bGreenWantedColor * rGreenRate));
							bRedWantedColor = (BYTE)(((FLOAT)bRedWantedColor * bRedRate));

							if (bBlueWantedColor > (m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift))
								bBlueWantedColor = (BYTE)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift);
							if (bGreenWantedColor > (m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift))
								bGreenWantedColor = (BYTE)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift);
							if (bRedWantedColor > (m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift))
								bRedWantedColor = (BYTE)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift);

							pwDst[((nYCnt - rcWanted.top) * (rcWanted.right - rcWanted.left)) + (nNewCurrWidth + nCheck)] = ((bRedWantedColor << m_stBitsMaskInfo.bRShift) |
								(bGreenWantedColor << m_stBitsMaskInfo.bGShift) |
								(bBlueWantedColor << m_stBitsMaskInfo.bBShift));
						}
						nNewCurrWidth += nCntCopyWord;
						x += nCntCopyWord;
					}
					// �� rcWanted.right ��Ϊ��׼���������򱻽�ȡ�������
					else if (nLastWidth < rcWanted.right && nCurrWidth > rcWanted.right)
					{
						for (INT nCheck = 0; nCheck < rcWanted.right - nLastWidth; nCheck++)
						{
							wPixel = wChooseColor;
							bBlueWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
							bGreenWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
							bRedWantedColor = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

							wPixel = pwSrc[x + nCheck];
							bBlueSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
							bGreenSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
							bRedSrc = (BYTE)((wPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

							rBlueRate = (FLOAT)((FLOAT)bBlueSrc / (FLOAT)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));
							rGreenRate = (FLOAT)((FLOAT)bGreenSrc / (FLOAT)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
							bRedRate = (FLOAT)((FLOAT)bRedSrc / (FLOAT)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));

							bBlueWantedColor = (BYTE)(((FLOAT)bBlueWantedColor * rBlueRate));
							bGreenWantedColor = (BYTE)(((FLOAT)bGreenWantedColor * rGreenRate));
							bRedWantedColor = (BYTE)(((FLOAT)bRedWantedColor * bRedRate));

							if (bBlueWantedColor > (m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift))
								bBlueWantedColor = (BYTE)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift);
							if (bGreenWantedColor > (m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift))
								bGreenWantedColor = (BYTE)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift);
							if (bRedWantedColor > (m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift))
								bRedWantedColor = (BYTE)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift);


							pwDst[((nYCnt - rcWanted.top) * (rcWanted.right - rcWanted.left)) + (nNewCurrWidth + nCheck)] = ((bRedWantedColor << m_stBitsMaskInfo.bRShift) |
								(bGreenWantedColor << m_stBitsMaskInfo.bGShift) |
								(bBlueWantedColor << m_stBitsMaskInfo.bBShift));
						}
						nNewCurrWidth += rcWanted.right - nLastWidth;
						x += nCntCopyWord;
					}
					else
					{
						x += nCntCopyWord;
					}
				}
			}
			else
			{
				return FALSE;
			}
		}
		// ���е�ĩβ�ƶ�����һ�еĿ�ͷ��
		nWidthEnd++;

		nWidthStart = nWidthEnd;
		nCurrWidth = 0;
		nNewCurrWidth = 0;
	}
	return TRUE;
}











BOOL CWHDXGraphicWindow::DrawWithImageForComp(RECT* prcSrc, WORD* pwSrc, LPDIRECT3DTEXTURE9 pddsDst)
{
	LPDIRECT3DSURFACE9 ddsDst = NULL;
	D3DLOCKED_RECT		lockedRect;

	if (pddsDst)
	{
		if (FAILED(pddsDst->GetSurfaceLevel(0, &ddsDst)))
		{
			return FALSE;
		}
	}
	else
	{
		return FALSE;
	}

	// DirectX 9.0 Ǩ�ƣ�ʹ��LockRect���Lock
	if (FAILED(ddsDst->LockRect(&lockedRect, NULL, 0)))
	{
		return FALSE;
	}

	// DirectX 9.0 Ǩ�ƣ�ʹ��pBits��Pitch
	WORD* pwdDst = (WORD*)lockedRect.pBits;
	LONG lWidth = lockedRect.Pitch >> 1;

	INT nYCnt = 0;
	INT nWidthStart = 0;
	INT nWidthEnd = 0;
	INT nCurrWidth = 0;
	INT nLastWidth = 0;
	INT nCntCopyWord = 0;

	// Y��ü���
	for (nYCnt = 0; nYCnt < prcSrc->top; nYCnt++)
	{
		nWidthEnd += pwSrc[nWidthStart];
		nWidthStart++;
		nWidthEnd++;
		nWidthStart = nWidthEnd;
	}

	// ȷ�� Y ����ʵ��ѭ���� Count��
	for (nYCnt = prcSrc->top; nYCnt < prcSrc->bottom; nYCnt++)
	{
		// ��ȡһ�еĳ��ȣ���λΪ�֣���
		nWidthEnd += pwSrc[nWidthStart];
		nWidthStart++;

		// �����еĳ�������Ļ����ʾ��
		for (INT x = nWidthStart; x < nWidthEnd; )
		{
			if (pwSrc[x] == 0xC0)
			{
				x++;
				nCntCopyWord = pwSrc[x];
				x++;
				nLastWidth = nCurrWidth;
				nCurrWidth += nCntCopyWord;
			}
			else if (pwSrc[x] == 0xC1)
			{
				x++;
				nCntCopyWord = pwSrc[x];
				x++;

				nLastWidth = nCurrWidth;
				nCurrWidth += nCntCopyWord;

				// �� prcSrc->left ��Ϊ��׼���������򱻽�ȡ�������
				if (nLastWidth <= prcSrc->left && prcSrc->left <= nCurrWidth)
				{
					if (((nYCnt - prcSrc->top) * lWidth) < 0)
						return FALSE;

					if (prcSrc->right <= nCurrWidth)
					{
						x += (prcSrc->left - nLastWidth);
						memcpy(&pwdDst[((nYCnt - prcSrc->top) * lWidth)], &pwSrc[x], sizeof(WORD) * (prcSrc->right - prcSrc->left));
						x += (nCurrWidth - prcSrc->left);
					}
					else
					{
						x += (prcSrc->left - nLastWidth);
						memcpy(&pwdDst[((nYCnt - prcSrc->top) * lWidth)], &pwSrc[x], sizeof(WORD) * (nCurrWidth - prcSrc->left));
						x += (nCurrWidth - prcSrc->left);
					}
				}
				// �� prcSrc->right ��Ϊ��׼���������򱻽�ȡ�������
				else if (nLastWidth < prcSrc->right && prcSrc->right < nCurrWidth)
				{
					memcpy(&pwdDst[((nYCnt - prcSrc->top) * lWidth) + (nLastWidth - prcSrc->left)], &pwSrc[x], sizeof(WORD) * (prcSrc->right - nLastWidth));
					x += nCntCopyWord;
				}
				// �� prcSrc->left �����������
				else if (nLastWidth < prcSrc->left && nCurrWidth <= prcSrc->left)
				{
					x += nCntCopyWord;
				}
				// �� prcSrc->right ���Ҳ�������
				else if (nLastWidth >= prcSrc->right && nCurrWidth > prcSrc->right)
				{
					x += nCntCopyWord;
				}
				// �� prcSrc->left ��� prcSrc->right ��֮��������
				else
				{
					memcpy(&pwdDst[((nYCnt - prcSrc->top) * lWidth) + (nLastWidth - prcSrc->left)], &pwSrc[x], sizeof(WORD) * nCntCopyWord);
					x += nCntCopyWord;
				}
			}
			else
			{
				// DirectX 9.0 Ǩ�ƣ�ʹ��UnlockRect���Unlock
				ddsDst->UnlockRect();
				return FALSE;
			}
		}
		// ���е�ĩβ�ƶ�����һ�еĿ�ͷ��
		nWidthEnd++;

		nWidthStart = nWidthEnd;
		nCurrWidth = 0;
	}

	// DirectX 9.0 Ǩ�ƣ�ʹ��UnlockRect���Unlock
	ddsDst->UnlockRect();
	return TRUE;
}















/******************************************************************************************************************

	������ : CWHDXGraphicWindow::DrawWithABlendF2orIntersectCompData()

	���� :
	�������� :

	Ŀ�� :
	���� : INT nX					: ��̨�������Ļ�����ʼλ�á�
			 INT nY
			 INT nSrcXSize			: Դͼ��ĳߴ硣
			 INT nSrcYSize
			 WORD* pwSrc			: Դͼ������ݡ�
			 INT nDstXSize			: Ŀ��ͼ��ĳߴ硣
			 INT nDstYSize
			 WORD* pwDst			: Ŀ��ͼ������ݡ�
			 WORD wSrcChooseColor1	: Դͼ���Ⱦɫͼ��1��ɫ��
			 WORD wSrcChooseColor2
			 WORD wDstChooseColor1	: Ŀ��ͼ���Ⱦɫͼ��1��ɫ��
			 WORD wDstChooseColor2
	��� : BOOL

	[����][�޸���] : �޸�����

*******************************************************************************************************************/
BOOL CWHDXGraphicWindow::DrawWithABlendForIntersectCompData(INT nSrcX, INT nSrcY,
	INT nSrcXSize, INT nSrcYSize, WORD* pwSrc,
	INT nDstX, INT nDstY,
	INT nDstXSize, INT nDstYSize, WORD* pwDst,
	WORD wClipWidth, WORD wClipHeight,
	BYTE bOpa, BOOL bFocused,
	WORD wSrcChooseColor1, WORD wSrcChooseColor2,
	WORD wDstChooseColor1, WORD wDstChooseColor2,
	WORD wSrcColor, WORD wDstColor)
{
	RECT rcSrc, rcDst;
	RECT rcSrcIntersect = { 0, 0, 0, 0 };
	RECT rcDstIntersect = { 0, 0, 0, 0 };
	WORD* pwdDst = NULL;

	INT	nSrcWidth = nSrcXSize;
	INT	nSrcHeight = nSrcYSize;
	INT	nXSrcOffset = 0;
	INT	nYSrcOffset = 0;

	INT	nDstWidth = nDstXSize;
	INT	nDstHeight = nDstYSize;
	INT	nXDstOffset = 0;
	INT	nYDstOffset = 0;

	INT	nEndX = wClipWidth - 1;
	INT	nEndY = wClipHeight - 1;
	INT	nStartX = 0;
	INT	nStartY = 0;

	WORD* pwDstSave = NULL;
	WORD* pwSrcSave = NULL;

	if (m_pddsOffscreenSurface != NULL)
	{
		// Source ����Ļ�ü�����
		if (nSrcX < nStartX)
		{
			nXSrcOffset = nStartX - nSrcX;
			nSrcWidth = nSrcXSize - nXSrcOffset;
		}
		if ((nSrcX + nSrcXSize - 1) > nEndX)
			nSrcWidth = nEndX - nSrcX - nXSrcOffset + 1;
		if (nSrcY < nStartY)
		{
			nYSrcOffset = nStartY - nSrcY;
			nSrcHeight = nSrcYSize - nYSrcOffset;
		}
		if ((nSrcY + nSrcYSize - 1) > nEndY)
			nSrcHeight = nEndY - nSrcY - nYSrcOffset + 1;

		if ((nSrcWidth > 0) && (nSrcHeight > 0))
		{
			// Source ��������á�
			rcSrc.left = nXSrcOffset;
			rcSrc.right = nXSrcOffset + nSrcWidth;
			rcSrc.top = nYSrcOffset;
			rcSrc.bottom = nYSrcOffset + nSrcHeight;

			// Destination ����Ļ�ü�����
			if (nDstX < nStartX)
			{
				nXDstOffset = nStartX - nDstX;
				nDstWidth = nDstXSize - nXDstOffset;
			}
			if ((nDstX + nDstXSize - 1) > nEndX)
				nDstWidth = nEndX - nDstX - nXDstOffset + 1;
			if (nDstY < nStartY)
			{
				nYDstOffset = nStartY - nDstY;
				nDstHeight = nDstYSize - nYDstOffset;
			}
			if ((nDstY + nDstYSize - 1) > nEndY)
				nDstHeight = nEndY - nDstY - nYDstOffset + 1;

			if ((nDstWidth > 0) && (nDstHeight > 0))
			{
				// Destination ��������á�
				rcDst.left = nXDstOffset;
				rcDst.right = nXDstOffset + nDstWidth;
				rcDst.top = nYDstOffset;
				rcDst.bottom = nYDstOffset + nDstHeight;

				// ��������ͼ����ص�����
				RECT rcTemp;

				// ���������Դ�����Ŀ������
				rcTemp.left = rcDst.left + (nDstX - nSrcX);
				rcTemp.top = rcDst.top + (nDstY - nSrcY);
				rcTemp.right = rcDst.right + (nDstX - nSrcX);
				rcTemp.bottom = rcDst.bottom + (nDstY - nSrcY);

				// ��Դ��������Ϊ��׼�������������εĽ�����Ρ�
				IntersectRect(&rcSrcIntersect, &rcSrc, &rcTemp);

				// ���������Ŀ�������Դ����
				rcTemp.left = rcSrc.left + (nSrcX - nDstX);
				rcTemp.top = rcSrc.top + (nSrcY - nDstY);
				rcTemp.right = rcSrc.right + (nSrcX - nDstX);
				rcTemp.bottom = rcSrc.bottom + (nSrcY - nDstY);

				// ��Ŀ���������Ϊ��׼�������������εĽ�����Ρ�
				IntersectRect(&rcDstIntersect, &rcTemp, &rcDst);

				D3DLOCKED_RECT lockedRect;
				LONG	lWidth;

				if (FAILED(m_pddsOffscreenSurface->LockRect(&lockedRect, NULL, 0)))
				{
					return FALSE;
				}

				WORD* pwdDst = (WORD*)lockedRect.pBits;
				lWidth = lockedRect.Pitch >> 1;

				pwSrcSave = new WORD[(rcSrcIntersect.right - rcSrcIntersect.left) * (rcSrcIntersect.bottom - rcSrcIntersect.top)];
				pwDstSave = new WORD[(rcDstIntersect.right - rcDstIntersect.left) * (rcDstIntersect.bottom - rcDstIntersect.top)];

				if (wSrcColor == 0XFFFF && wDstColor == 0XFFFF)
				{
					DrawWithImageForCompToMem(rcSrcIntersect, (WORD*)pwSrc, wSrcChooseColor1, wSrcChooseColor2, pwSrcSave);
					DrawWithImageForCompToMem(rcDstIntersect, (WORD*)pwDst, wDstChooseColor1, wDstChooseColor2, pwDstSave);
				}
				else if (wSrcColor != 0XFFFF && wDstColor == 0XFFFF)
				{
					DrawWithImageForCompColorToMem(rcSrcIntersect, (WORD*)pwSrc, wSrcColor, pwSrcSave);
					DrawWithImageForCompToMem(rcDstIntersect, (WORD*)pwDst, wDstChooseColor1, wDstChooseColor2, pwDstSave);
				}
				else if (wSrcColor == 0XFFFF && wDstColor != 0XFFFF)
				{
					DrawWithImageForCompToMem(rcSrcIntersect, (WORD*)pwSrc, wSrcChooseColor1, wSrcChooseColor2, pwSrcSave);
					DrawWithImageForCompColorToMem(rcDstIntersect, (WORD*)pwDst, wDstColor, pwDstSave);
				}
				else
				{
					DrawWithImageForCompColorToMem(rcSrcIntersect, (WORD*)pwSrc, wSrcColor, pwSrcSave);
					DrawWithImageForCompColorToMem(rcDstIntersect, (WORD*)pwDst, wDstColor, pwDstSave);
				}

				BYTE bRedDst, bGreenDst, bBlueDst;
				BYTE bRedSrc, bGreenSrc, bBlueSrc;
				WORD wSrcPixel, wDstPixel;
				FLOAT rBlueRate, rGreenRate, bRedRate;

				for (INT nYCnt = 0; nYCnt < rcSrcIntersect.bottom - rcSrcIntersect.top; nYCnt++)
					for (INT nXCnt = 0; nXCnt < rcSrcIntersect.right - rcSrcIntersect.left; nXCnt++)
					{
						wSrcPixel = pwSrcSave[nYCnt * (rcSrcIntersect.right - rcSrcIntersect.left) + nXCnt];
						wDstPixel = pwDstSave[nYCnt * (rcDstIntersect.right - rcDstIntersect.left) + nXCnt];
						if (wSrcPixel != 0 && wDstPixel != 0)
						{
							bBlueSrc = (BYTE)((wSrcPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
							bGreenSrc = (BYTE)((wSrcPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
							bRedSrc = (BYTE)((wSrcPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

							if (bFocused)
							{
								rBlueRate = (FLOAT)((FLOAT)bBlueSrc / (FLOAT)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift));
								rGreenRate = (FLOAT)((FLOAT)bGreenSrc / (FLOAT)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift));
								bRedRate = (FLOAT)((FLOAT)bRedSrc / (FLOAT)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift));

								bBlueSrc = (BYTE)((FLOAT)bBlueSrc + ((FLOAT)bBlueSrc * rBlueRate));
								bGreenSrc = (BYTE)((FLOAT)bGreenSrc + ((FLOAT)bGreenSrc * rGreenRate));
								bRedSrc = (BYTE)((FLOAT)bRedSrc + ((FLOAT)bRedSrc * bRedRate));

								if (bBlueSrc > (m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift))
									bBlueSrc = (BYTE)(m_stBitsMaskInfo.dwBMask >> m_stBitsMaskInfo.bBShift);
								if (bGreenSrc > (m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift))
									bGreenSrc = (BYTE)(m_stBitsMaskInfo.dwGMask >> m_stBitsMaskInfo.bGShift);
								if (bRedSrc > (m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift))
									bRedSrc = (BYTE)(m_stBitsMaskInfo.dwRMask >> m_stBitsMaskInfo.bRShift);

								// ����ý���ʱ������ Opacity ֵ��
								bOpa = 40;
							}

							bBlueDst = (BYTE)((wDstPixel & m_stBitsMaskInfo.dwBMask) >> m_stBitsMaskInfo.bBShift);
							bGreenDst = (BYTE)((wDstPixel & m_stBitsMaskInfo.dwGMask) >> m_stBitsMaskInfo.bGShift);
							bRedDst = (BYTE)((wDstPixel & m_stBitsMaskInfo.dwRMask) >> m_stBitsMaskInfo.bRShift);

							bBlueDst = (BYTE)((bOpa * (bBlueDst - bBlueSrc) + 100 * bBlueSrc) / 100);
							bGreenDst = (BYTE)((bOpa * (bGreenDst - bGreenSrc) + 100 * bGreenSrc) / 100);
							bRedDst = (BYTE)((bOpa * (bRedDst - bRedSrc) + 100 * bRedSrc) / 100);

							pwdDst[((nYCnt + nSrcY + rcSrcIntersect.top) * lWidth) + (rcSrcIntersect.left + nSrcX + nXCnt)] =
								((bRedDst << m_stBitsMaskInfo.bRShift) |
									(bGreenDst << m_stBitsMaskInfo.bGShift) |
									(bBlueDst << m_stBitsMaskInfo.bBShift));
						}
					}

				SAFE_DELETEARR(pwSrcSave);
				SAFE_DELETEARR(pwDstSave);

				// DirectX 9.0 Ǩ�ƣ�ʹ��UnlockRect���Unlock
				m_pddsOffscreenSurface->UnlockRect();
				return TRUE;
			}
		}
	}
	return FALSE;
}

HRESULT CWHDXGraphicWindow::Present()
{
	HRESULT hr;

	if (NULL == m_pd3dDevice)
	{
		return E_POINTER;
	}

	// DirectX 9.0 Ǩ�ƣ��ȸ�����Ⱦ���浽��̨������
	hr = CopyOffscreeToBackBuffer();
	if (FAILED(hr))
	{
		return hr;
	}
	// DirectX 9.0 Ǩ�ƣ�ʹ��Direct3D9��Present����
	if (m_bScreenModeFlag & _DXG_SCREENMODE_WINDOW)
	{
		// ����ģʽ�����ֵ����ڿͻ���
		RECT rcSrc = { 0, 0, m_stDisplayInfo.wWidth, m_stDisplayInfo.wHeight };
		hr = m_pd3dDevice->Present(&rcSrc, &m_rcWindow, m_hWnd, NULL);
	}
	else
	{
		// ȫ��ģʽ�����������󻺳�
		hr = m_pd3dDevice->Present(NULL, NULL, NULL, NULL);
	}

	// DirectX 9.0 Ǩ�ƣ��豸��ʧ����
	if (hr == D3DERR_DEVICELOST || hr == D3DERR_DEVICENOTRESET)
	{
		hr = HandleDeviceLost();
		if (FAILED(hr))
		{
			return hr;
		}

		if (m_bScreenModeFlag & _DXG_SCREENMODE_WINDOW)
		{
			RECT rcSrc = { 0, 0, m_stDisplayInfo.wWidth, m_stDisplayInfo.wHeight };
			hr = m_pd3dDevice->Present(&rcSrc, &m_rcWindow, m_hWnd, NULL);
		}
		else
		{
			hr = m_pd3dDevice->Present(NULL, NULL, NULL, NULL);
		}
	}

	return hr;
}


/******************************************************************************************************************

  Callback Function

*******************************************************************************************************************/

/******************************************************************************************************************

	DirectX 9.0 Ǩ�ƣ��豸��ʧ��������ʵ��

*******************************************************************************************************************/

// �����豸��ʧ����Ҫ����
HRESULT CWHDXGraphicWindow::HandleDeviceLost()
{
	if (!m_pd3dDevice)
		return E_FAIL;

	HRESULT hr = m_pd3dDevice->TestCooperativeLevel();

	if (hr == D3DERR_DEVICELOST)
	{
		// �豸��ʧ���ȴ��ָ�
		return hr;
	}
	else if (hr == D3DERR_DEVICENOTRESET)
	{
		// �豸��������
		ReleaseDefaultResources();

		// �����豸��ʹ�ñ���ĳ��ֲ���
		hr = m_pd3dDevice->Reset(&m_d3dpp);
		if (FAILED(hr))
			return hr;

		// ʹ��ͳһ����Դ�ָ�����
		RestoreDefaultResources();

		// ����������Ⱦ״̬
		Init3DDeviceObjects();

		// ����RGB������Ϣ
		m_stBitsMaskInfo = GetRGBMaskInfoIN16Bits(m_pddsBackBuffer);

	}

	return hr;
}


/******************************************************************************************************************

	DirectX 9.0 Ǩ�ƣ���Դ�����Ż�ʵ��

*******************************************************************************************************************/

// ����Ĭ��������D3DPOOL_DEFAULT��
HRESULT CWHDXGraphicWindow::CreateDefaultTexture(UINT Width, UINT Height, D3DFORMAT Format, LPDIRECT3DTEXTURE9* ppTexture)
{
	if (!m_pd3dDevice || !ppTexture)
		return E_FAIL;

	// DirectX 9.0 Ǩ�ƣ����������ߴ�
	DWORD dwWidth = Width;
	DWORD dwHeight = Height;

	// DirectX 9.0 Ǩ�ƣ���ȡ�豸����
	D3DCAPS9 d3dCaps;
	if (FAILED(m_pd3dDevice->GetDeviceCaps(&d3dCaps)))
	{
		return E_FAIL;
	}

	// ����Ƿ���Ҫ2���ݴη�����
	if (!(d3dCaps.TextureCaps & D3DPTEXTURECAPS_NONPOW2CONDITIONAL))
	{
		// �Զ���ɿ����ݴη����㺯�������ѭ����
		auto GetNextPowerOfTwo = [](UINT n) {
			if (n == 0) return 1U;
			--n;
			n |= n >> 1;
			n |= n >> 2;
			n |= n >> 4;
			n |= n >> 8;
			n |= n >> 16;
			return ++n;
			};
		dwWidth = GetNextPowerOfTwo(Width);
		dwHeight = GetNextPowerOfTwo(Height);
	}

	// ������������ߴ�
	DWORD dwMaxWidth = d3dCaps.MaxTextureWidth;
	DWORD dwMaxHeight = d3dCaps.MaxTextureHeight;
	dwWidth = min(dwWidth, (dwMaxWidth ? dwMaxWidth : 256));
	dwHeight = min(dwHeight, (dwMaxHeight ? dwMaxHeight : 256));

	// ����Ƿ���Ҫ����������
	if (d3dCaps.TextureCaps & D3DPTEXTURECAPS_SQUAREONLY)
	{
		if (dwWidth > dwHeight)
		{
			dwHeight = dwWidth;
		}
		else
		{
			dwWidth = dwHeight;
		}
	}

	// DirectX 9.0 Ǩ�ƣ���������
	return m_pd3dDevice->CreateTexture(
		dwWidth,					// ��������
		dwHeight,					// �����߶�
		1,                          // �����㼶������1����������mipmap��
		0,                          // ��;��0��ʾͨ������
		D3DFMT_A1R5G5B5,			// 16λARGB��ʽ��֧��͸��
		D3DPOOL_MANAGED,			// �ڴ�أ���DX9�Զ����������ԭTEXTUREMANAGE��
		ppTexture,					// ������������
		NULL);
}

// DirectX 9.0 Ǩ�ƣ���ʼ��Ⱦ
HRESULT CWHDXGraphicWindow::BeginRender()
{
	if (!m_pd3dDevice)
		return E_FAIL;

	return m_pd3dDevice->BeginScene();
}

// DirectX 9.0 Ǩ�ƣ�������Ⱦ
HRESULT CWHDXGraphicWindow::EndRender()
{
	if (!m_pd3dDevice)
		return E_FAIL;

	return m_pd3dDevice->EndScene();
}

HRESULT CWHDXGraphicWindow::CopyOffscreeToBackBuffer()
{
	if (!m_pd3dDevice || !m_pddsBackBuffer || !m_pddsOffscreenSurface)
		return E_FAIL;

	// ����1��������ǰ��Ⱦ���ͷ�GPU�Ժ�̨�����ռ��
	m_pd3dDevice->EndScene();

	// ����2����ȡ��ǰ��ȾĿ�꣨���У�����ʱ���
	LPDIRECT3DSURFACE9 pOldRT = NULL; // ���浱ǰ��ȾĿ��
	HRESULT hr = m_pd3dDevice->GetRenderTarget(0, &pOldRT);
	if (SUCCEEDED(hr) && pOldRT)
	{
		// ����ǰ��ȾĿ���Ǻ�̨���壬��ʱ�л�ΪNULL������󶨣�
		if (pOldRT == m_pddsBackBuffer)
		{
			m_pd3dDevice->SetRenderTarget(0, NULL);
		}

		SAFE_RELEASE(pOldRT); // �ͷ����ã������ڴ�й©
	}

	// DirectX 9.0 Ǩ�ƣ�ʹ��StretchRect����Ⱦ��꼴��
	// ��Ϊ����Ⱦ��ꡢ��̨�����ݱ�ʹ��StretchRect
	hr = m_pd3dDevice->StretchRect(
		m_pddsOffscreenSurface,		// Դ���棨��������
		NULL,                   // Դ���Σ��������棩
		m_pddsBackBuffer,      // Ŀ����棨��̨���壩
		NULL,                   // Ŀ����Σ��������棩
		D3DTEXF_NONE			// �����й���
	);

	if (FAILED(hr))
	{
		TCHAR szError[512];
		const TCHAR* errorMsg = TEXT("Unknown error");

		switch (hr)
		{
		case D3DERR_INVALIDCALL:
			errorMsg = TEXT("D3DERR_INVALIDCALL - Invalid parameters or surface formats");
			break;
		case D3DERR_DEVICELOST:
			errorMsg = TEXT("D3DERR_DEVICELOST - Device lost");
			break;
		case E_OUTOFMEMORY:
			errorMsg = TEXT("E_OUTOFMEMORY - Out of memory");
			break;
		}

		D3DSURFACE_DESC surfDesc1; // ��̨����
		D3DSURFACE_DESC surfDesc2; // ��������
		m_pddsBackBuffer->GetDesc(&surfDesc1);
		m_pddsOffscreenSurface->GetDesc(&surfDesc2);

		// ��ӡ�ؼ������������VS���Դ��ڣ�
		wsprintf(szError,
			TEXT("BackBuffer: Format=%d, Width=%d, Height=%d, Pool=%d, Usage=%d\n")
			TEXT("OffscreenSurface: Format=%d, Width=%d, Height=%d, Pool=%d, Usage=%d\n")
			TEXT("StretchRect failed: 0x%08X - %s\n"),
			surfDesc1.Format, surfDesc1.Width, surfDesc1.Height, surfDesc1.Pool, surfDesc1.Usage,
			surfDesc2.Format, surfDesc2.Width, surfDesc2.Height, surfDesc2.Pool, surfDesc2.Usage,
			hr, errorMsg
		);
		OutputDebugString(szError);
		MessageBox(m_hWnd, szError, TEXT("StretchRect Error"), MB_OK | MB_ICONERROR);
	}

	// DirectX 9.0 Ǩ�ƣ�������ȾĿ��
	if (FAILED(hr = m_pd3dDevice->SetRenderTarget(0, m_pddsBackBuffer)))
	{
		MessageBox(m_hWnd, TEXT("Failed to set render target."), TEXT("MirDXG"), MB_ICONERROR | MB_OK);
		return E_FAIL;
	}

	return hr;
}

// �ͷ�����Ĭ���ڴ���е���Դ
VOID CWHDXGraphicWindow::ReleaseDefaultResources()
{
	// �ͷź󻺳���棨��Ĭ���ڴ���У�
	SAFE_RELEASE(m_pddsOffscreenSurface);
	SAFE_RELEASE(m_pddsBackBuffer);

	// �ͷ����ģ����棨��Ĭ���ڴ���У�
	SAFE_RELEASE(m_pDepthStencilSurface);

	// ����Ӧ���ͷ�������D3DPOOL_DEFAULT�д�������Դ
	// �綯̬���������㻺������������������
	// ע�⣺D3DPOOL_MANAGED�е���Դ����Ҫ�ֶ��ͷ�
}

// �ָ�����Ĭ���ڴ���е���Դ
VOID CWHDXGraphicWindow::RestoreDefaultResources()
{
	if (!m_pd3dDevice)
		return;

	// ���»�ȡ�󻺳����
	m_pd3dDevice->GetBackBuffer(0, 0, D3DBACKBUFFER_TYPE_MONO, &m_pddsBackBuffer);

	// ���»�ȡ�򴴽����ģ�����
	if (m_bDeviceModeFlag & _DXG_DEVICEMODE_ZBUFFER)
	{
		HRESULT hr = m_pd3dDevice->GetDepthStencilSurface(&m_pDepthStencilSurface);
		if (FAILED(hr))
		{
			// �����ȡʧ�ܣ������µ����ģ�����
			m_pd3dDevice->CreateDepthStencilSurface(
				m_stDisplayInfo.wWidth,
				m_stDisplayInfo.wHeight,
				D3DFMT_D16,
				D3DMULTISAMPLE_NONE,
				0,
				TRUE,
				&m_pDepthStencilSurface,
				NULL);

			m_pd3dDevice->SetDepthStencilSurface(m_pDepthStencilSurface);
		}
	}

	// ����Ӧ�����´���������D3DPOOL_DEFAULT�е���Դ
	// �綯̬���������㻺������������������
}


/******************************************************************************************************************

	DirectX 9.0 Ǩ�ƣ���ɫ��֧��ʵ��

*******************************************************************************************************************/

// �򵥵Ķ�����ɫ�����루HLSL��
static const char* g_strVertexShader =
"struct VS_INPUT\n"
"{\n"
"    float4 pos : POSITION;\n"
"    float4 color : COLOR0;\n"
"    float2 tex : TEXCOORD0;\n"
"};\n"
"\n"
"struct VS_OUTPUT\n"
"{\n"
"    float4 pos : POSITION;\n"
"    float4 color : COLOR0;\n"
"    float2 tex : TEXCOORD0;\n"
"};\n"
"\n"
"VS_OUTPUT main(VS_INPUT input)\n"
"{\n"
"    VS_OUTPUT output;\n"
"    output.pos = input.pos;\n"
"    output.color = input.color;\n"
"    output.tex = input.tex;\n"
"    return output;\n"
"}\n";

// �򵥵�������ɫ�����루HLSL��
static const char* g_strPixelShader =
"sampler2D texSampler : register(s0);\n"
"\n"
"struct PS_INPUT\n"
"{\n"
"    float4 color : COLOR0;\n"
"    float2 tex : TEXCOORD0;\n"
"};\n"
"\n"
"float4 main(PS_INPUT input) : COLOR0\n"
"{\n"
"    float4 texColor = tex2D(texSampler, input.tex);\n"
"    return texColor * input.color;\n"
"}\n";

// ������ɫ��
HRESULT CWHDXGraphicWindow::CreateShaders()
{
	if (!m_pd3dDevice)
		return E_FAIL;

	HRESULT hr;
	LPD3DXBUFFER pVertexShaderCode = NULL;
	LPD3DXBUFFER pPixelShaderCode = NULL;
	LPD3DXBUFFER pErrorBuffer = NULL;

	// ���붥����ɫ��
	hr = D3DXCompileShader(
		g_strVertexShader,
		strlen(g_strVertexShader),
		NULL,
		NULL,
		"main",
		"vs_2_0",
		0,
		&pVertexShaderCode,
		&pErrorBuffer,
		NULL);

	if (FAILED(hr))
	{
		if (pErrorBuffer)
		{
			MessageBoxA(m_hWnd, (char*)pErrorBuffer->GetBufferPointer(), "Vertex Shader Compile Error", MB_OK);
			pErrorBuffer->Release();
		}
		return hr;
	}

	// ����������ɫ��
	hr = m_pd3dDevice->CreateVertexShader(
		(DWORD*)pVertexShaderCode->GetBufferPointer(),
		&m_pVertexShader);

	pVertexShaderCode->Release();

	if (FAILED(hr))
		return hr;

	// ����������ɫ��
	hr = D3DXCompileShader(
		g_strPixelShader,
		strlen(g_strPixelShader),
		NULL,
		NULL,
		"main",
		"ps_2_0",
		0,
		&pPixelShaderCode,
		&pErrorBuffer,
		NULL);

	if (FAILED(hr))
	{
		if (pErrorBuffer)
		{
			MessageBoxA(m_hWnd, (char*)pErrorBuffer->GetBufferPointer(), "Pixel Shader Compile Error", MB_OK);
			pErrorBuffer->Release();
		}
		return hr;
	}

	// ����������ɫ��
	hr = m_pd3dDevice->CreatePixelShader(
		(DWORD*)pPixelShaderCode->GetBufferPointer(),
		&m_pPixelShader);

	pPixelShaderCode->Release();

	return hr;
}

// �������㻺����
HRESULT CWHDXGraphicWindow::CreateDefaultBuffer()
{
	if (!m_pd3dDevice)
		return E_FAIL;

	// �������㻺������������4�����㣬���ڻ���һ���ı��Σ�
	HRESULT hr = m_pd3dDevice->CreateVertexBuffer(
		4 * sizeof(SPRITE_VERTEX),  // ��������С
		D3DUSAGE_DYNAMIC | D3DUSAGE_WRITEONLY,  // ��;
		D3DFVF_SPRITE_VERTEX,                   // �����ʽ
		D3DPOOL_DEFAULT,                        // Ĭ���ڴ��
		&m_pVertexBuffer,                       // ���������ָ��
		NULL);

	if (FAILED(hr))
		return hr;

	// ����������������6�����������ڻ���������������ɵ��ı��Σ�
	hr = m_pd3dDevice->CreateIndexBuffer(
		6 * sizeof(WORD),						// ��������С
		D3DUSAGE_WRITEONLY,						// ��;
		D3DFMT_INDEX16,							// 16λ������ʽ
		D3DPOOL_DEFAULT,						// Ĭ���ڴ��
		&m_pIndexBuffer,                        // ���������ָ��
		NULL);

	if (SUCCEEDED(hr))
	{
		// �����������
		WORD* pIndices;
		if (SUCCEEDED(m_pIndexBuffer->Lock(0, 0, (void**)&pIndices, 0)))
		{
			// ��������������ı��Σ�0-1-2, 0-2-3
			pIndices[0] = 0; pIndices[1] = 1; pIndices[2] = 2;
			pIndices[3] = 0; pIndices[4] = 2; pIndices[5] = 3;
			m_pIndexBuffer->Unlock();
		}
	}

	return hr;
}

// �ͷ���ɫ����Դ
VOID CWHDXGraphicWindow::ReleaseShaders()
{
	SAFE_RELEASE(m_pVertexShader);
	SAFE_RELEASE(m_pPixelShader);
	SAFE_RELEASE(m_pVertexBuffer);
	SAFE_RELEASE(m_pIndexBuffer);
	SAFE_RELEASE(m_pVertexDecl);

	// �ͷ�������������
	ReleaseBatchBuffers();
}

// ���þ�����Ⱦ״̬
HRESULT CWHDXGraphicWindow::SetupSpriteRendering()
{
	if (!m_pd3dDevice)
		return E_FAIL;

	// ������Ⱦ״̬
	m_pd3dDevice->SetRenderState(D3DRS_LIGHTING, FALSE);
	m_pd3dDevice->SetRenderState(D3DRS_ALPHABLENDENABLE, TRUE);
	m_pd3dDevice->SetRenderState(D3DRS_SRCBLEND, D3DBLEND_SRCALPHA);
	m_pd3dDevice->SetRenderState(D3DRS_DESTBLEND, D3DBLEND_INVSRCALPHA);

	// ���������׶�״̬
	m_pd3dDevice->SetTextureStageState(0, D3DTSS_COLOROP, D3DTOP_MODULATE);
	m_pd3dDevice->SetTextureStageState(0, D3DTSS_COLORARG1, D3DTA_TEXTURE);
	m_pd3dDevice->SetTextureStageState(0, D3DTSS_COLORARG2, D3DTA_DIFFUSE);
	m_pd3dDevice->SetTextureStageState(0, D3DTSS_ALPHAOP, D3DTOP_MODULATE);
	m_pd3dDevice->SetTextureStageState(0, D3DTSS_ALPHAARG1, D3DTA_TEXTURE);
	m_pd3dDevice->SetTextureStageState(0, D3DTSS_ALPHAARG2, D3DTA_DIFFUSE);

	// ���ò�����״̬
	m_pd3dDevice->SetSamplerState(0, D3DSAMP_MINFILTER, D3DTEXF_POINT);
	m_pd3dDevice->SetSamplerState(0, D3DSAMP_MAGFILTER, D3DTEXF_POINT);

	return S_OK;
}

// ��Ⱦ����
HRESULT CWHDXGraphicWindow::RenderSprite(FLOAT x, FLOAT y, FLOAT width, FLOAT height, LPDIRECT3DTEXTURE9 pTexture)
{
	if (!m_pd3dDevice || !m_pVertexBuffer || !m_pIndexBuffer || !pTexture)
		return E_FAIL;

	// ��������
	m_pd3dDevice->SetTexture(0, pTexture);

	// �������㻺��������䶥������
	SPRITE_VERTEX* pVertices;
	if (FAILED(m_pVertexBuffer->Lock(0, 0, (void**)&pVertices, D3DLOCK_DISCARD)))
		return E_FAIL;

	// ������Ļ���꣨DirectX 9.0����Ҫ��ȥ0.5����ƫ�ƣ�
	FLOAT left = x - 0.5f;
	FLOAT top = y - 0.5f;
	FLOAT right = x + width - 0.5f;
	FLOAT bottom = y + height - 0.5f;

	// ����ĸ����㣨���ϡ����ϡ����¡����£�
	pVertices[0].x = left;   pVertices[0].y = top;    pVertices[0].z = 0.0f; pVertices[0].rhw = 1.0f;
	pVertices[0].color = 0xFFFFFFFF; pVertices[0].u = 0.0f; pVertices[0].v = 0.0f;

	pVertices[1].x = right;  pVertices[1].y = top;    pVertices[1].z = 0.0f; pVertices[1].rhw = 1.0f;
	pVertices[1].color = 0xFFFFFFFF; pVertices[1].u = 1.0f; pVertices[1].v = 0.0f;

	pVertices[2].x = right;  pVertices[2].y = bottom; pVertices[2].z = 0.0f; pVertices[2].rhw = 1.0f;
	pVertices[2].color = 0xFFFFFFFF; pVertices[2].u = 1.0f; pVertices[2].v = 1.0f;

	pVertices[3].x = left;   pVertices[3].y = bottom; pVertices[3].z = 0.0f; pVertices[3].rhw = 1.0f;
	pVertices[3].color = 0xFFFFFFFF; pVertices[3].u = 0.0f; pVertices[3].v = 1.0f;

	m_pVertexBuffer->Unlock();

	// ������Դ������������
	m_pd3dDevice->SetStreamSource(0, m_pVertexBuffer, 0, sizeof(SPRITE_VERTEX));
	m_pd3dDevice->SetIndices(m_pIndexBuffer);
	m_pd3dDevice->SetFVF(D3DFVF_SPRITE_VERTEX);

	// ������Ⱦ״̬
	SetupSpriteRendering();

	// ������������ͼԪ
	HRESULT hr = m_pd3dDevice->DrawIndexedPrimitive(
		D3DPT_TRIANGLELIST,        // ͼԪ����
		0,                         // ������ʼ����
		0,                         // ��С��������
		4,                         // ��������
		0,                         // ����������ʼ����
		2);                        // ͼԪ���� (ÿ��������3������)

	return hr;
}


/******************************************************************************************************************

	DirectX 9.0 Ǩ�ƣ��������Ż�ʵ��

*******************************************************************************************************************/

// ����������������
HRESULT CWHDXGraphicWindow::CreateBatchBuffers()
{
	if (!m_pd3dDevice)
		return E_FAIL;

	// ���������������С������ͬʱ��Ⱦ�ľ���������
	m_dwMaxBatchSize = 1000;

	// �������������㻺������ÿ��������Ҫ4�����㣩
	HRESULT hr = m_pd3dDevice->CreateVertexBuffer(
		m_dwMaxBatchSize * 4 * sizeof(SPRITE_VERTEX),
		D3DUSAGE_DYNAMIC | D3DUSAGE_WRITEONLY,
		D3DFVF_SPRITE_VERTEX,
		D3DPOOL_DEFAULT,
		&m_pBatchVertexBuffer,
		NULL);

	if (FAILED(hr))
		return hr;

	// ����������������������ÿ��������Ҫ6��������
	hr = m_pd3dDevice->CreateIndexBuffer(
		m_dwMaxBatchSize * 6 * sizeof(WORD),
		D3DUSAGE_WRITEONLY,
		D3DFMT_INDEX16,
		D3DPOOL_DEFAULT,
		&m_pBatchIndexBuffer,
		NULL);

	if (SUCCEEDED(hr))
	{
		// ����������ݣ����о��鹲����ͬ������ģʽ��
		WORD* pIndices;
		if (SUCCEEDED(m_pBatchIndexBuffer->Lock(0, 0, (void**)&pIndices, 0)))
		{
			for (DWORD i = 0; i < m_dwMaxBatchSize; i++)
			{
				WORD baseVertex = (WORD)(i * 4);
				DWORD baseIndex = i * 6;

				// ��������������ı��Σ�0-1-2, 0-2-3
				pIndices[baseIndex + 0] = baseVertex + 0;
				pIndices[baseIndex + 1] = baseVertex + 1;
				pIndices[baseIndex + 2] = baseVertex + 2;
				pIndices[baseIndex + 3] = baseVertex + 0;
				pIndices[baseIndex + 4] = baseVertex + 2;
				pIndices[baseIndex + 5] = baseVertex + 3;
			}
			m_pBatchIndexBuffer->Unlock();
		}
	}

	// ��ʼ��������״̬
	m_dwCurrentBatchSize = 0;
	m_pCurrentTexture = NULL;

	return hr;
}

// �ͷ�������������
VOID CWHDXGraphicWindow::ReleaseBatchBuffers()
{
	SAFE_RELEASE(m_pBatchVertexBuffer);
	SAFE_RELEASE(m_pBatchIndexBuffer);
	m_dwCurrentBatchSize = 0;
	m_pCurrentTexture = NULL;
}

// ��ʼ������
HRESULT CWHDXGraphicWindow::BeginBatch()
{
	m_dwCurrentBatchSize = 0;
	m_pCurrentTexture = NULL;
	return S_OK;
}

// ����������
HRESULT CWHDXGraphicWindow::EndBatch()
{
	// ˢ��ʣ���������
	return FlushBatch();
}

// ���Ӿ��鵽������
HRESULT CWHDXGraphicWindow::AddSpriteToBatch(FLOAT x, FLOAT y, FLOAT width, FLOAT height, LPDIRECT3DTEXTURE9 pTexture)
{
	if (!m_pBatchVertexBuffer || !pTexture)
		return E_FAIL;

	// ��������ı����������������ˢ�µ�ǰ������
	if ((m_pCurrentTexture && m_pCurrentTexture != pTexture) || m_dwCurrentBatchSize >= m_dwMaxBatchSize)
	{
		HRESULT hr = FlushBatch();
		if (FAILED(hr))
			return hr;
	}

	// ���õ�ǰ����
	m_pCurrentTexture = pTexture;

	// �������㻺���������Ӷ�������
	SPRITE_VERTEX* pVertices;
	DWORD lockOffset = m_dwCurrentBatchSize * 4 * sizeof(SPRITE_VERTEX);
	DWORD lockSize = 4 * sizeof(SPRITE_VERTEX);

	if (FAILED(m_pBatchVertexBuffer->Lock(lockOffset, lockSize, (void**)&pVertices, D3DLOCK_NOOVERWRITE)))
		return E_FAIL;

	// ������Ļ����
	FLOAT left = x - 0.5f;
	FLOAT top = y - 0.5f;
	FLOAT right = x + width - 0.5f;
	FLOAT bottom = y + height - 0.5f;

	// ����ĸ�����
	pVertices[0].x = left;   pVertices[0].y = top;    pVertices[0].z = 0.0f; pVertices[0].rhw = 1.0f;
	pVertices[0].color = 0xFFFFFFFF; pVertices[0].u = 0.0f; pVertices[0].v = 0.0f;

	pVertices[1].x = right;  pVertices[1].y = top;    pVertices[1].z = 0.0f; pVertices[1].rhw = 1.0f;
	pVertices[1].color = 0xFFFFFFFF; pVertices[1].u = 1.0f; pVertices[1].v = 0.0f;

	pVertices[2].x = right;  pVertices[2].y = bottom; pVertices[2].z = 0.0f; pVertices[2].rhw = 1.0f;
	pVertices[2].color = 0xFFFFFFFF; pVertices[2].u = 1.0f; pVertices[2].v = 1.0f;

	pVertices[3].x = left;   pVertices[3].y = bottom; pVertices[3].z = 0.0f; pVertices[3].rhw = 1.0f;
	pVertices[3].color = 0xFFFFFFFF; pVertices[3].u = 0.0f; pVertices[3].v = 1.0f;

	m_pBatchVertexBuffer->Unlock();

	// ������������С
	m_dwCurrentBatchSize++;

	return S_OK;
}

// ˢ��������
HRESULT CWHDXGraphicWindow::FlushBatch()
{
	if (!m_pd3dDevice || m_dwCurrentBatchSize == 0 || !m_pCurrentTexture)
		return S_OK;

	// ��������
	m_pd3dDevice->SetTexture(0, m_pCurrentTexture);

	// ������Դ������������
	m_pd3dDevice->SetStreamSource(0, m_pBatchVertexBuffer, 0, sizeof(SPRITE_VERTEX));
	m_pd3dDevice->SetIndices(m_pBatchIndexBuffer);
	m_pd3dDevice->SetFVF(D3DFVF_SPRITE_VERTEX);

	// ������Ⱦ״̬
	SetupSpriteRendering();

	// �����������е����о���
	HRESULT hr = m_pd3dDevice->DrawIndexedPrimitive(
		D3DPT_TRIANGLELIST,        // ͼԪ����
		0,                         // ������ʼ����
		0,                         // ��С��������
		m_dwCurrentBatchSize * 4,  // ��������
		0,                         // ����������ʼ����
		m_dwCurrentBatchSize * 2); // ͼԪ���� (ÿ��������3������)

	// ����������״̬
	m_dwCurrentBatchSize = 0;
	m_pCurrentTexture = NULL;

	return hr;
}


/******************************************************************************************************************

	DirectX 9.0 Ǩ�ƣ����D3DCommon����Ⱦ״̬����

*******************************************************************************************************************/

// DirectX 9.0 Ǩ�ƣ�ö������������
HRESULT CWHDXGraphicWindow::EnumerateD3D9Adapters()
{
	// ������ʱ��Direct3D9��������ö��
	LPDIRECT3D9 pD3D = Direct3DCreate9(D3D_SDK_VERSION);
	if (!pD3D)
		return E_FAIL;

	// �����豸����
	g_bNumDevices = 0;
	ZeroMemory(g_stDXGEnumDeviceInfo, sizeof(g_stDXGEnumDeviceInfo));

	// ö������������
	UINT uAdapterCount = pD3D->GetAdapterCount();
	for (UINT i = 0; i < uAdapterCount && g_bNumDevices < _MAX_DEVICES; i++)
	{
		D3DADAPTER_IDENTIFIER9 adapterInfo;
		if (SUCCEEDED(pD3D->GetAdapterIdentifier(i, 0, &adapterInfo)))
		{
			DXG_ENUM_DEVICEINFO* pDeviceInfo = &g_stDXGEnumDeviceInfo[g_bNumDevices];
			ZeroMemory(pDeviceInfo, sizeof(DXG_ENUM_DEVICEINFO));

			// ������������Ϣ
			lstrcpyn(pDeviceInfo->szDriverDesc, adapterInfo.Description, 255);
			lstrcpyn(pDeviceInfo->szDeviceDesc, "Hardware T&L", 255);
			pDeviceInfo->uAdapter = i;

			// ��ȡ�豸����
			if (SUCCEEDED(pD3D->GetDeviceCaps(i, D3DDEVTYPE_HAL, &pDeviceInfo->d3dCaps)))
			{
				pDeviceInfo->f3DHardware = TRUE;
				pDeviceInfo->deviceType = D3DDEVTYPE_HAL;
			}
			else if (SUCCEEDED(pD3D->GetDeviceCaps(i, D3DDEVTYPE_REF, &pDeviceInfo->d3dCaps)))
			{
				pDeviceInfo->f3DHardware = FALSE;
				pDeviceInfo->deviceType = D3DDEVTYPE_REF;
			}

			g_bNumDevices++;
		}
	}

	pD3D->Release();
	return (g_bNumDevices > 0) ? S_OK : E_FAIL;
}
